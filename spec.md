# Specification: Dynamic FX Clustering Application (v2)

## 1. Overview

This document outlines the specifications for a new web application designed for dynamic clustering analysis of Forex (FX) market data. The application will leverage the existing `matrix_QP` project for data acquisition from MetaTrader 5 and will provide a dedicated user interface for visualizing and analyzing market regimes.

The primary goal is to offer a professional-grade tool for traders and analysts to track how relationships between currency pairs evolve over time, identify significant market shifts, and understand the drivers behind them through two distinct but related lenses: **Correlation Clustering** and **Volatility Clustering**.

## 2. Core Features

The application will be built around two main analysis modules:
1.  **Currency Pair Clustering:** Grouping currency pairs based on their real-time price correlations, presented in a four-panel dashboard.
2.  **Volatility Regime Clustering:** Grouping trading days or time periods based on similar volatility patterns, presented in a historical calendar view.

## 3. Architecture & Technology

To achieve high performance and a responsive user experience, the application will use a hybrid Python/Rust architecture.

*   **Rust Core Engine (`cluster_logic`):**
    *   A new Rust library will be created to handle all computationally intensive tasks.
    *   **Responsibilities:** Calculating log returns, rolling correlation and volatility matrices, performing hierarchical clustering, and calculating cluster statistics.
    *   This library will be compiled into a shared object (`.so`, `.dll`) that can be called from Python.

*   **Python Backend Orchestrator:**
    *   The main application logic will remain in Python for rapid development and ease of integration.
    *   **Responsibilities:**
        *   Reusing the existing `matrix_QP/mt5_connector.py` module to fetch data from MetaTrader 5.
        *   Passing the raw data to the Rust engine for processing.
        *   Receiving the computed results (cluster assignments, stats, etc.) from the Rust engine.
        *   Managing the application state and history.
        *   Serving the data to the frontend via a web framework.

*   **Frontend:** A web-based dashboard built with **Dash/Plotly** for its strength in creating data-rich, interactive analytical applications.

*   **Data Flow:**
    1.  The Python backend connects to MT5 and fetches minute-by-minute price data.
    2.  This raw data is passed to the Rust Core Engine.
    3.  The Rust engine performs all heavy calculations: log returns, correlation matrices, distance matrices, hierarchical clustering, and statistics.
    4.  The Rust engine returns a structured data object with the results to Python.
    5.  The Python backend updates its state and serves the processed data to the Dash frontend for visualization.

## 4. UI/UX: Correlation Clustering View

The main dashboard will be a four-panel layout designed for comprehensive, real-time analysis of currency correlations.

### 4.1. Main Dashboard Layout (4-Panel)

*   **Top-Left: Dynamic Dendrogram:**
    *   Displays the current hierarchical clustering of currency pairs.
    *   Interactive (zoom/pan) and color-coded to match the Sankey diagram.

*   **Top-Right: Cluster Evolution Timeline (Sankey Diagram):**
    *   Visualizes the merging and splitting of clusters over time.
    *   The thickness of the flowing bands represents the number of currencies in each cluster.

*   **Bottom-Left: Detailed Statistics Panel:**
    *   When a cluster is selected (by clicking on the dendrogram or Sankey), this panel displays detailed metrics.
    *   **Statistics to show:**
        *   List of member currency pairs.
        *   Average intra-cluster correlation.
        *   Overall cluster volatility (e.g., average standard deviation of members).
        *   Cluster "lifespan" (how long the current membership has been stable).

*   **Bottom-Right: Event Log:**
    *   A chronological list of all significant cluster changes (merges/splits).
    *   Clicking an event opens a "before and after" comparison modal.

## 5. UI/UX: Volatility Regime Clustering View

This feature will have its own dedicated tab within the application.

### 5.1. Data Representation and Logic

*   **Daily Volatility Profile:** Each trading day is represented by a 24-point vector of hourly realized volatility.
*   **Daily Clustering:** The Rust engine will cluster a history of these daily vectors (e.g., using K-Means) to define a set of "volatility regimes."
*   **Intraday Matching:** The backend will provide a mapping of each hour of a selected day to the archetype regime it most closely matches.

### 5.2. User Interface

*   **Main View: Historical Regime Calendar:**
    *   A calendar where each day is color-coded by its assigned volatility regime.
*   **Interaction: Clicking a Day:**
    *   Opens a modal showing the daily volatility chart.
    *   The chart will be annotated to show which intraday regime each hour belongs to.

## 6. Data Handling and Error Management

*   **Data Persistence:** Historical results (cluster assignments, event logs, volatility regimes) will be saved to a local database (e.g., SQLite) for quick retrieval.
*   **Error Handling:** The system must gracefully handle connection losses to MT5 or calculation errors from the Rust engine, displaying clear notifications in the UI without crashing.