"""
Comprehensive Test Suite for Sankey Diagram Functionality

Tests the cluster evolution visualization utilities and dashboard integration
for the Dynamic FX Clustering Application.
"""

import sys
import os
sys.path.append('.')

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict
import logging

# Import test modules
from clustering.sankey_utils import (
    extract_cluster_evolution_data,
    create_cluster_evolution_sankey,
    create_cluster_timeline_chart,
    get_cluster_evolution_summary
)
from clustering.state_manager import ClusteringState, StateManager
from config import MARKET_TIMEZONE

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_clustering_states(count: int = 10) -> List[ClusteringState]:
    """Create test clustering states for evolution analysis"""
    
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'NZDUSD', 'USDCAD', 'EURJPY']
    states = []
    
    base_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=2)
    
    for i in range(count):
        # Create evolving cluster assignments
        if i < 3:
            # Initial clustering: 3 clusters
            cluster_assignments = [0, 0, 1, 1, 2, 2, 1, 0]
        elif i < 6:
            # Middle clustering: 2 clusters (consolidation)
            cluster_assignments = [0, 0, 1, 1, 1, 1, 1, 0]
        else:
            # Final clustering: 4 clusters (fragmentation)
            cluster_assignments = [0, 0, 1, 2, 3, 3, 2, 1]
        
        # Create correlation matrix
        correlation_matrix = np.random.rand(len(symbols), len(symbols))
        correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2
        np.fill_diagonal(correlation_matrix, 1.0)
        
        # Create volatility profiles
        volatility_profiles = {symbol: np.random.uniform(0.5, 2.0) for symbol in symbols}
        
        state = ClusteringState(
            timestamp=base_time + timedelta(minutes=i * 15),  # 15-minute intervals
            correlation_matrix=correlation_matrix,
            cluster_assignments=cluster_assignments,
            cluster_count=len(set(cluster_assignments)),
            symbols=symbols,
            volatility_profiles=volatility_profiles,
            regime_stability=np.random.uniform(0.6, 0.9),
            data_quality_score=np.random.uniform(0.8, 1.0)
        )
        
        states.append(state)
    
    return states


def test_sankey_utilities():
    """Test 1: Sankey Utilities - Core functionality"""
    
    print("\n" + "="*60)
    print("TEST 1: SANKEY UTILITIES - CORE FUNCTIONALITY")
    print("="*60)
    
    try:
        # Create test data
        print("1.1 Creating test clustering states...")
        test_states = create_test_clustering_states(12)
        print(f"✅ Created {len(test_states)} test states")
        
        # Test evolution data extraction
        print("\n1.2 Testing cluster evolution data extraction...")
        evolution_data = extract_cluster_evolution_data(
            test_states,
            time_window_minutes=45,  # Group states into 45-minute windows
            min_states=2
        )
        
        if evolution_data:
            print(f"✅ Evolution data extracted successfully:")
            print(f"   - Time windows: {len(evolution_data['time_windows'])}")
            print(f"   - Transitions: {len(evolution_data['transitions'])}")
            print(f"   - Total timespan: {evolution_data['total_timespan']:.1f} hours")
            print(f"   - State count: {evolution_data['state_count']}")
        else:
            print("❌ Failed to extract evolution data")
            return False
        
        # Test Sankey diagram creation
        print("\n1.3 Testing Sankey diagram creation...")
        sankey_fig = create_cluster_evolution_sankey(
            evolution_data,
            title="Test Cluster Evolution",
            height=400,
            width=600
        )
        
        print(f"✅ Sankey diagram created:")
        print(f"   - Figure type: {type(sankey_fig)}")
        print(f"   - Number of traces: {len(sankey_fig.data)}")
        print(f"   - Title: {sankey_fig.layout.title.text}")
        
        # Test timeline chart creation
        print("\n1.4 Testing timeline chart creation...")
        timeline_fig = create_cluster_timeline_chart(
            evolution_data,
            title="Test Timeline Chart",
            height=300
        )
        
        print(f"✅ Timeline chart created:")
        print(f"   - Figure type: {type(timeline_fig)}")
        print(f"   - Number of traces: {len(timeline_fig.data)}")
        print(f"   - Title: {timeline_fig.layout.title.text}")
        
        # Test evolution summary
        print("\n1.5 Testing evolution summary generation...")
        summary = get_cluster_evolution_summary(evolution_data)
        
        print(f"✅ Evolution summary generated:")
        print(f"   - Timespan: {summary.get('timespan_hours', 0):.1f} hours")
        print(f"   - Avg cluster count: {summary.get('avg_cluster_count', 0):.1f}")
        print(f"   - Total transitions: {summary.get('total_transitions', 0)}")
        print(f"   - Max stability: {summary.get('max_stability', 0):.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test 1 failed: {str(e)}")
        return False


def test_state_manager_integration():
    """Test 2: State Manager Integration - Historical data access"""
    
    print("\n" + "="*60)
    print("TEST 2: STATE MANAGER INTEGRATION")
    print("="*60)
    
    try:
        # Initialize state manager
        print("2.1 Initializing StateManager...")
        state_manager = StateManager(
            max_history_size=50,
            persistence_dir="test_data/sankey_state"
        )
        print("✅ StateManager initialized")
        
        # Add test states to state manager
        print("\n2.2 Adding test states to StateManager...")
        test_states = create_test_clustering_states(8)
        
        for state in test_states:
            # Simulate state updates
            state_manager.update_state(
                correlation_matrix=state.correlation_matrix,
                cluster_assignments=state.cluster_assignments,
                symbols=state.symbols,
                volatility_profiles=state.volatility_profiles,
                data_quality_score=state.data_quality_score
            )
        
        print(f"✅ Added {len(test_states)} states to StateManager")
        
        # Test historical data retrieval
        print("\n2.3 Testing historical data retrieval...")
        history = state_manager.get_state_history(limit=20)
        print(f"✅ Retrieved {len(history)} historical states")
        
        # Test evolution analysis with real state manager data
        print("\n2.4 Testing evolution analysis with StateManager data...")
        if len(history) >= 3:
            evolution_data = extract_cluster_evolution_data(
                history,
                time_window_minutes=30,
                min_states=2
            )
            
            if evolution_data:
                print(f"✅ Evolution analysis successful:")
                print(f"   - Windows: {len(evolution_data['time_windows'])}")
                print(f"   - Transitions: {len(evolution_data['transitions'])}")
                
                # Create Sankey with real data
                sankey_fig = create_cluster_evolution_sankey(evolution_data)
                print(f"✅ Sankey diagram created with real data")
            else:
                print("⚠️  Insufficient data for evolution analysis")
        else:
            print("⚠️  Not enough historical states for testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Test 2 failed: {str(e)}")
        return False


def test_dashboard_integration():
    """Test 3: Dashboard Integration - Callback simulation"""
    
    print("\n" + "="*60)
    print("TEST 3: DASHBOARD INTEGRATION")
    print("="*60)
    
    try:
        # Import dashboard components
        print("3.1 Testing dashboard imports...")
        import run_clustering_app
        print("✅ Dashboard imports successful")
        
        # Test with mock clustering data
        print("\n3.2 Testing Sankey callback with mock data...")
        mock_clustering_data = {
            'current_state': {
                'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'],
                'cluster_assignments': [0, 0, 1, 1],
                'correlation_matrix': [[1.0, 0.8, 0.3, 0.2],
                                     [0.8, 1.0, 0.2, 0.3],
                                     [0.3, 0.2, 1.0, 0.7],
                                     [0.2, 0.3, 0.7, 1.0]]
            }
        }
        
        # Test different timeframes
        timeframes = ['1h', '6h', '24h']
        for timeframe in timeframes:
            print(f"\n3.3 Testing timeframe: {timeframe}")
            
            try:
                sankey_fig = run_clustering_app.update_sankey_diagram(
                    mock_clustering_data, 
                    timeframe
                )
                
                print(f"✅ Sankey callback executed for {timeframe}:")
                print(f"   - Figure type: {type(sankey_fig)}")
                print(f"   - Title: {sankey_fig.layout.title.text}")
                print(f"   - Height: {sankey_fig.layout.height}")
                
            except Exception as e:
                print(f"⚠️  Callback error for {timeframe}: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test 3 failed: {str(e)}")
        return False


def test_edge_cases():
    """Test 4: Edge Cases - Error handling and fallbacks"""
    
    print("\n" + "="*60)
    print("TEST 4: EDGE CASES - ERROR HANDLING")
    print("="*60)
    
    try:
        # Test with insufficient data
        print("4.1 Testing with insufficient data...")
        minimal_states = create_test_clustering_states(1)
        evolution_data = extract_cluster_evolution_data(minimal_states, min_states=3)
        
        if evolution_data is None:
            print("✅ Correctly handled insufficient data")
        else:
            print("⚠️  Should have returned None for insufficient data")
        
        # Test with empty states
        print("\n4.2 Testing with empty states...")
        empty_evolution = extract_cluster_evolution_data([], min_states=2)
        
        if empty_evolution is None:
            print("✅ Correctly handled empty state list")
        else:
            print("⚠️  Should have returned None for empty states")
        
        # Test Sankey creation with invalid data
        print("\n4.3 Testing Sankey creation with invalid data...")
        try:
            invalid_data = {'invalid': 'data'}
            sankey_fig = create_cluster_evolution_sankey(invalid_data)
            print(f"✅ Gracefully handled invalid data: {type(sankey_fig)}")
        except Exception as e:
            print(f"✅ Expected error for invalid data: {str(e)[:50]}")
        
        # Test timeline chart with minimal data
        print("\n4.4 Testing timeline chart with minimal data...")
        minimal_evolution = {
            'time_windows': [
                {'window_start': datetime.now(), 'cluster_count': 2, 
                 'avg_stability': 0.8, 'avg_quality': 0.9}
            ]
        }
        
        timeline_fig = create_cluster_timeline_chart(minimal_evolution)
        print(f"✅ Timeline chart created with minimal data: {type(timeline_fig)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test 4 failed: {str(e)}")
        return False


def run_all_sankey_tests():
    """Run comprehensive Sankey diagram test suite"""
    
    print("🧪 STARTING COMPREHENSIVE SANKEY DIAGRAM TESTS")
    print("=" * 80)
    
    test_results = []
    
    # Run all tests
    test_results.append(("Sankey Utilities", test_sankey_utilities()))
    test_results.append(("State Manager Integration", test_state_manager_integration()))
    test_results.append(("Dashboard Integration", test_dashboard_integration()))
    test_results.append(("Edge Cases", test_edge_cases()))
    
    # Summary
    print("\n" + "="*80)
    print("🎯 SANKEY DIAGRAM TEST RESULTS SUMMARY")
    print("="*80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL SANKEY DIAGRAM TESTS PASSED!")
        print("\n📊 Enhanced features verified:")
        print("   ✅ Cluster evolution data extraction")
        print("   ✅ Interactive Sankey diagram visualization")
        print("   ✅ Timeline chart fallback functionality")
        print("   ✅ Dashboard integration and callbacks")
        print("   ✅ State manager historical data access")
        print("   ✅ Error handling and edge cases")
        print("   ✅ Evolution summary statistics")
        return True
    else:
        print(f"⚠️  {total - passed} tests failed. Please review the output above.")
        return False


if __name__ == "__main__":
    success = run_all_sankey_tests()
    sys.exit(0 if success else 1)
