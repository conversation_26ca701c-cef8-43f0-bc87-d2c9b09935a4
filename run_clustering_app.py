"""
Dynamic FX Clustering Application - Main Dashboard
==================================================

Interactive 4-panel Dash dashboard for real-time forex market regime detection
with dendrogram visualization, Sankey diagrams, statistics panel, and event log.

Panels:
1. Top-Left: Interactive Dendrogram (cluster structure)
2. Top-Right: Sankey Diagram (cluster evolution over time)
3. Bottom-Left: Statistics Panel (metrics and performance)
4. Bottom-Right: Event Log (regime changes and alerts)
"""

import dash
from dash import dcc, html, Input, Output, State, callback_context
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import threading
import time
from typing import Dict, List, Optional, Any

# Import clustering components
from clustering.clustering_engine import ClusteringEngine
from clustering.state_manager import ClusteringState, ClusteringEvent
from clustering.dendrogram_utils import create_interactive_dendrogram, create_cluster_scatter_plot, calculate_cluster_statistics
from clustering.sankey_utils import (
    extract_cluster_evolution_data,
    create_cluster_evolution_sankey,
    create_cluster_timeline_chart,
    get_cluster_evolution_summary
)
from config import (
    CURRENCY_PAIRS, DATA_UPDATE_INTERVAL, CHART_UPDATE_INTERVAL,
    CHART_THEME, CHART_HEIGHT, CHART_WIDTH, MARKET_TIMEZONE
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import Rust clustering functions
try:
    import cluster_core
    RUST_AVAILABLE = True
    logger.info("Rust cluster_core module loaded successfully")
except ImportError as e:
    RUST_AVAILABLE = False
    logger.warning(f"Rust cluster_core module not available: {e}")
    logger.warning("Falling back to Python-only clustering")

# =============================================================================
# APPLICATION INITIALIZATION
# =============================================================================

# Initialize Dash app with Bootstrap theme
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.BOOTSTRAP, dbc.icons.FONT_AWESOME],
    title="Dynamic FX Clustering Dashboard",
    update_title="Updating..."
)

# Initialize clustering engine
clustering_engine = ClusteringEngine(
    symbols=CURRENCY_PAIRS,
    event_threshold=0.7,
    min_data_quality=0.8,
    persistence_dir="data/clustering"
)

# Global state for dashboard
dashboard_state = {
    'last_update': None,
    'update_count': 0,
    'connection_status': 'disconnected',
    'auto_update': True,
    'selected_timeframe': '24h'
}

# =============================================================================
# LAYOUT COMPONENTS
# =============================================================================

def create_header():
    """Create dashboard header with title and controls"""
    return dbc.Row([
        dbc.Col([
            html.H1("Dynamic FX Clustering Dashboard", className="text-primary mb-0"),
            html.P("Real-time forex market regime detection", className="text-muted")
        ], width=8),
        dbc.Col([
            dbc.ButtonGroup([
                dbc.Button("Connect", id="btn-connect", color="success", size="sm"),
                dbc.Button("Disconnect", id="btn-disconnect", color="danger", size="sm", disabled=True),
                dbc.Button("Refresh", id="btn-refresh", color="primary", size="sm")
            ], className="mb-2"),
            html.Div([
                dbc.Badge("Disconnected", id="status-badge", color="danger", className="me-2"),
                html.Small(id="last-update-text", className="text-muted")
            ])
        ], width=4, className="text-end")
    ], className="mb-4")

def create_control_panel():
    """Create control panel with timeframe and update settings"""
    return dbc.Card([
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    html.Label("Timeframe:", className="form-label"),
                    dcc.Dropdown(
                        id="timeframe-dropdown",
                        options=[
                            {'label': 'Last 1 Hour', 'value': '1h'},
                            {'label': 'Last 6 Hours', 'value': '6h'},
                            {'label': 'Last 24 Hours', 'value': '24h'},
                            {'label': 'Last 3 Days', 'value': '3d'},
                            {'label': 'Last Week', 'value': '7d'}
                        ],
                        value='24h',
                        clearable=False
                    )
                ], width=3),
                dbc.Col([
                    html.Label("Auto Update:", className="form-label"),
                    dbc.Switch(
                        id="auto-update-switch",
                        value=True,
                        className="mt-2"
                    )
                ], width=2),
                dbc.Col([
                    html.Label("Cluster Count:", className="form-label"),
                    html.H4(id="cluster-count-display", children="0", className="text-primary")
                ], width=2),
                dbc.Col([
                    html.Label("Data Quality:", className="form-label"),
                    html.H4(id="data-quality-display", children="0.0%", className="text-info")
                ], width=2),
                dbc.Col([
                    html.Label("Regime Stability:", className="form-label"),
                    html.H4(id="stability-display", children="0.0%", className="text-success")
                ], width=3)
            ])
        ])
    ], className="mb-3")

def create_main_panels():
    """Create the 4-panel main dashboard layout"""
    return [
        dbc.Row([
            # Top row
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Cluster Dendrogram", className="mb-0"),
                        dbc.Badge("Interactive", color="info", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="dendrogram-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Cluster Evolution", className="mb-0"),
                        dbc.Badge("Sankey Flow", color="warning", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="sankey-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-3"),
        dbc.Row([
            # Bottom row
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Performance Statistics", className="mb-0"),
                        dbc.Badge("Real-time", color="success", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="statistics-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Event Log", className="mb-0"),
                        dbc.Badge(id="event-count-badge", children="0 Events", color="secondary", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="event-log-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=6)
        ])
    ]

# =============================================================================
# MAIN LAYOUT
# =============================================================================

app.layout = dbc.Container([
    # Header
    create_header(),
    
    # Control Panel
    create_control_panel(),
    
    # Main Panels
    create_main_panels(),
    
    # Hidden components for data storage and intervals
    dcc.Store(id="clustering-data-store"),
    dcc.Store(id="dashboard-state-store", data=dashboard_state),
    dcc.Store(id="selected-cluster-store", data=None),
    dcc.Interval(
        id="data-update-interval",
        interval=DATA_UPDATE_INTERVAL,
        n_intervals=0,
        disabled=False
    ),
    dcc.Interval(
        id="chart-update-interval", 
        interval=CHART_UPDATE_INTERVAL,
        n_intervals=0,
        disabled=False
    )
], fluid=True)

# =============================================================================
# CALLBACK FUNCTIONS
# =============================================================================

@app.callback(
    [Output("status-badge", "children"),
     Output("status-badge", "color"),
     Output("btn-connect", "disabled"),
     Output("btn-disconnect", "disabled"),
     Output("last-update-text", "children")],
    [Input("btn-connect", "n_clicks"),
     Input("btn-disconnect", "n_clicks"),
     Input("data-update-interval", "n_intervals")],
    [State("dashboard-state-store", "data")]
)
def update_connection_status(connect_clicks, disconnect_clicks, n_intervals, state_data):
    """Update connection status and control buttons"""
    ctx = callback_context

    if not ctx.triggered:
        return "Disconnected", "danger", False, True, "Never updated"

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

    if trigger_id == "btn-connect":
        try:
            if clustering_engine.connect():
                dashboard_state['connection_status'] = 'connected'
                return "Connected", "success", True, False, f"Connected at {datetime.now().strftime('%H:%M:%S')}"
            else:
                return "Connection Failed", "warning", False, True, "Connection attempt failed"
        except Exception as e:
            logger.error(f"Connection error: {str(e)}")
            return "Error", "danger", False, True, f"Error: {str(e)[:50]}"

    elif trigger_id == "btn-disconnect":
        try:
            clustering_engine.disconnect()
            dashboard_state['connection_status'] = 'disconnected'
            return "Disconnected", "danger", False, True, f"Disconnected at {datetime.now().strftime('%H:%M:%S')}"
        except Exception as e:
            logger.error(f"Disconnection error: {str(e)}")
            return "Error", "danger", False, True, f"Error: {str(e)[:50]}"

    # Regular status check
    if dashboard_state['connection_status'] == 'connected':
        last_update = dashboard_state.get('last_update')
        if last_update:
            return "Connected", "success", True, False, f"Last update: {last_update}"
        else:
            return "Connected", "success", True, False, "Connected - No data yet"
    else:
        return "Disconnected", "danger", False, True, "Not connected"


@app.callback(
    [Output("clustering-data-store", "data"),
     Output("cluster-count-display", "children"),
     Output("data-quality-display", "children"),
     Output("stability-display", "children")],
    [Input("data-update-interval", "n_intervals"),
     Input("btn-refresh", "n_clicks")],
    [State("auto-update-switch", "value"),
     State("timeframe-dropdown", "value")]
)
def update_clustering_data(n_intervals, refresh_clicks, auto_update, timeframe):
    """Fetch and update clustering data"""
    ctx = callback_context

    # Skip update if auto-update is disabled and not a manual refresh
    if not auto_update and not (ctx.triggered and 'btn-refresh' in ctx.triggered[0]['prop_id']):
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # Skip if not connected
    if dashboard_state['connection_status'] != 'connected':
        return {}, "0", "0.0%", "0.0%"

    try:
        # Parse timeframe
        timeframe_hours = {
            '1h': 1, '6h': 6, '24h': 24, '3d': 72, '7d': 168
        }.get(timeframe, 24)

        # Run clustering analysis
        event = clustering_engine.run_clustering_analysis(
            hours_back=timeframe_hours,
            use_weekend_fallback=True
        )

        # Get current status
        status = clustering_engine.get_current_status()
        current_state = clustering_engine.state_manager.get_current_state()

        # Update dashboard state
        dashboard_state['last_update'] = datetime.now().strftime('%H:%M:%S')
        dashboard_state['update_count'] += 1

        # Prepare data for storage
        clustering_data = {
            'status': status,
            'current_state': {
                'timestamp': current_state.timestamp.isoformat() if current_state else None,
                'cluster_count': current_state.cluster_count if current_state else 0,
                'cluster_assignments': current_state.cluster_assignments if current_state else [],
                'symbols': current_state.symbols if current_state else [],
                'regime_stability': current_state.regime_stability if current_state else 0.0,
                'data_quality_score': current_state.data_quality_score if current_state else 0.0,
                'correlation_matrix': current_state.correlation_matrix.tolist() if current_state and current_state.correlation_matrix is not None else []
            } if current_state else None,
            'recent_events': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'description': event.description,
                    'significance_score': event.significance_score,
                    'affected_pairs': event.affected_pairs
                }
                for event in clustering_engine.state_manager.get_recent_events(hours_back=timeframe_hours)
            ],
            'last_update': dashboard_state['last_update']
        }

        # Format display values
        cluster_count = current_state.cluster_count if current_state else 0
        data_quality = f"{(current_state.data_quality_score * 100):.1f}%" if current_state else "0.0%"
        stability = f"{(current_state.regime_stability * 100):.1f}%" if current_state else "0.0%"

        logger.info(f"Data updated: {cluster_count} clusters, quality={data_quality}, stability={stability}")

        return clustering_data, str(cluster_count), data_quality, stability

    except Exception as e:
        logger.error(f"Error updating clustering data: {str(e)}")
        return {}, "Error", "Error", "Error"


@app.callback(
    Output("dendrogram-chart", "figure"),
    [Input("clustering-data-store", "data"),
     Input("chart-update-interval", "n_intervals")]
)
def update_dendrogram(clustering_data, n_intervals):
    """Update dendrogram visualization with enhanced hierarchical clustering"""
    if not clustering_data or not clustering_data.get('current_state'):
        return create_cluster_scatter_plot(
            symbols=[],
            cluster_assignments=[],
            title="Dendrogram - No Data Available",
            height=CHART_HEIGHT
        )

    try:
        current_state = clustering_data['current_state']
        symbols = current_state.get('symbols', [])
        cluster_assignments = current_state.get('cluster_assignments', [])
        correlation_matrix = np.array(current_state.get('correlation_matrix', []))

        if len(symbols) == 0 or len(correlation_matrix) == 0:
            return create_cluster_scatter_plot(
                symbols=[],
                cluster_assignments=[],
                title="Dendrogram - Insufficient Data",
                height=CHART_HEIGHT
            )

        # Get linkage matrix if available from clustering engine
        linkage_matrix = None
        if RUST_AVAILABLE and hasattr(clustering_engine, 'last_linkage_matrix'):
            linkage_matrix = getattr(clustering_engine, 'last_linkage_matrix', None)

        # Try to create interactive dendrogram first
        try:
            if len(symbols) >= 3 and correlation_matrix.shape[0] >= 3:
                # Create true dendrogram visualization
                fig = create_interactive_dendrogram(
                    correlation_matrix=correlation_matrix,
                    symbols=symbols,
                    linkage_matrix=linkage_matrix,
                    cluster_assignments=cluster_assignments,
                    title=f"Currency Clustering Dendrogram ({len(set(cluster_assignments))} clusters)",
                    height=CHART_HEIGHT
                )

                # Add cluster statistics as annotations
                if len(cluster_assignments) > 0:
                    cluster_stats = calculate_cluster_statistics(symbols, cluster_assignments, correlation_matrix)
                    stats_text = []
                    for cluster_id, stats in cluster_stats.items():
                        stats_text.append(f"Cluster {cluster_id}: {stats['size']} pairs (avg corr: {stats['avg_correlation']:.2f})")

                    if stats_text:
                        fig.add_annotation(
                            text="<br>".join(stats_text[:5]),  # Show first 5 clusters
                            xref="paper", yref="paper",
                            x=0.02, y=0.98,
                            xanchor='left', yanchor='top',
                            showarrow=False,
                            font=dict(size=9, color="lightgray"),
                            bgcolor="rgba(0,0,0,0.5)",
                            bordercolor="gray",
                            borderwidth=1
                        )

                return fig

        except Exception as dendrogram_error:
            logger.warning(f"Could not create dendrogram, falling back to scatter plot: {dendrogram_error}")

        # Fallback to cluster scatter plot
        fig = create_cluster_scatter_plot(
            symbols=symbols,
            cluster_assignments=cluster_assignments,
            correlation_matrix=correlation_matrix,
            title=f"Currency Pair Clusters ({len(set(cluster_assignments))} clusters)",
            height=CHART_HEIGHT
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating dendrogram: {str(e)}")
        return create_cluster_scatter_plot(
            symbols=[],
            cluster_assignments=[],
            title=f"Dendrogram - Error: {str(e)[:50]}",
            height=CHART_HEIGHT
        )


@app.callback(
    Output("sankey-chart", "figure"),
    [Input("clustering-data-store", "data"),
     Input("timeframe-dropdown", "value")]
)
def update_sankey_diagram(clustering_data, timeframe):
    """Update Sankey diagram showing cluster evolution over time"""
    if not clustering_data:
        fig = go.Figure()
        fig.update_layout(
            title="Cluster Evolution - No Data Available",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color="white")
        )
        return fig

    try:
        # Get timeframe parameters
        timeframe_hours = {
            '1h': 1, '6h': 6, '24h': 24, '3d': 72, '7d': 168
        }.get(timeframe, 24)

        # Get historical states from state manager
        state_history = clustering_engine.state_manager.get_state_history(limit=200)

        if len(state_history) < 3:
            # Not enough historical data - create informational chart
            fig = go.Figure()
            fig.add_annotation(
                text=f"Collecting data...<br>Need at least 3 states for evolution analysis<br>Current: {len(state_history)} states",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False,
                font=dict(size=16, color="white"),
                bgcolor="rgba(255,255,255,0.1)",
                bordercolor="white",
                borderwidth=1
            )
            fig.update_layout(
                title=f"Cluster Evolution - Collecting Data ({timeframe})",
                template=CHART_THEME,
                height=CHART_HEIGHT,
                paper_bgcolor="rgba(0,0,0,0)",
                plot_bgcolor="rgba(0,0,0,0)",
                font=dict(color="white")
            )
            return fig

        # Filter states by timeframe
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=timeframe_hours)
        recent_states = [
            state for state in state_history
            if state.timestamp >= cutoff_time
        ]

        if len(recent_states) < 2:
            # Use all available states if timeframe filter is too restrictive
            recent_states = state_history[-10:]  # Last 10 states

        # Extract cluster evolution data
        evolution_data = extract_cluster_evolution_data(
            recent_states,
            time_window_minutes=max(30, timeframe_hours * 60 // 10),  # Adaptive window size
            min_states=2
        )

        if evolution_data is None:
            # Fallback to timeline chart if Sankey data insufficient
            logger.info("Insufficient data for Sankey, creating timeline chart")
            fig = create_cluster_timeline_chart(
                {'time_windows': [{'window_start': s.timestamp, 'cluster_count': s.cluster_count,
                                  'avg_stability': s.regime_stability, 'avg_quality': s.data_quality_score}
                                 for s in recent_states]},
                title=f"Cluster Timeline ({timeframe})",
                height=CHART_HEIGHT
            )
            return fig

        # Create enhanced Sankey diagram
        fig = create_cluster_evolution_sankey(
            evolution_data,
            title=f"Currency Cluster Evolution ({timeframe})",
            height=CHART_HEIGHT,
            width=CHART_WIDTH
        )

        # Add evolution summary as annotation
        summary = get_cluster_evolution_summary(evolution_data)
        summary_text = (
            f"Windows: {summary.get('time_windows', 0)} | "
            f"Avg Clusters: {summary.get('avg_cluster_count', 0):.1f} | "
            f"Transitions: {summary.get('total_transitions', 0)}"
        )

        fig.add_annotation(
            text=summary_text,
            xref="paper", yref="paper",
            x=0.02, y=0.98, xanchor='left', yanchor='top',
            showarrow=False,
            font=dict(size=10, color="white"),
            bgcolor="rgba(0,0,0,0.7)",
            bordercolor="white",
            borderwidth=1
        )

        return fig

    except Exception as e:
        logger.error(f"Error creating Sankey diagram: {str(e)}")
        fig = go.Figure()
        fig.update_layout(
            title=f"Cluster Evolution - Error: {str(e)[:50]}",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color="white")
        )
        return fig


@app.callback(
    Output("selected-cluster-store", "data"),
    [Input("dendrogram-chart", "clickData"),
     Input("sankey-chart", "clickData")],
    [State("clustering-data-store", "data")]
)
def handle_cluster_selection(dendrogram_click, sankey_click, clustering_data):
    """Handle cluster selection from dendrogram or Sankey diagram clicks"""
    ctx = callback_context

    if not ctx.triggered or not clustering_data:
        return None

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

    try:
        selected_cluster = None

        if trigger_id == "dendrogram-chart" and dendrogram_click:
            # Extract cluster information from dendrogram click
            point_data = dendrogram_click['points'][0]

            # For dendrogram, we need to map the clicked point to a cluster
            # This requires the current cluster assignments
            current_state = clustering_data.get('current_state', {})
            symbols = current_state.get('symbols', [])
            cluster_assignments = current_state.get('cluster_assignments', [])

            if 'text' in point_data:
                # Find the symbol that was clicked
                clicked_symbol = point_data['text']
                if clicked_symbol in symbols:
                    symbol_index = symbols.index(clicked_symbol)
                    if symbol_index < len(cluster_assignments):
                        selected_cluster = {
                            'cluster_id': cluster_assignments[symbol_index],
                            'source': 'dendrogram',
                            'clicked_symbol': clicked_symbol
                        }

        elif trigger_id == "sankey-chart" and sankey_click:
            # Extract cluster information from Sankey click
            point_data = sankey_click['points'][0]

            # For Sankey, extract cluster ID from the node label
            if 'label' in point_data:
                label = point_data['label']
                # Parse cluster ID from label (format: "Cluster X (Y pairs)")
                import re
                cluster_match = re.search(r'Cluster (\d+)', label)
                if cluster_match:
                    selected_cluster = {
                        'cluster_id': int(cluster_match.group(1)),
                        'source': 'sankey',
                        'node_label': label
                    }

        return selected_cluster

    except Exception as e:
        logger.error(f"Error handling cluster selection: {str(e)}")
        return None


@app.callback(
    Output("statistics-panel", "children"),
    [Input("clustering-data-store", "data"),
     Input("selected-cluster-store", "data")]
)
def update_statistics_panel(clustering_data, selected_cluster):
    """Update statistics panel with cluster-specific or general performance statistics"""
    if not clustering_data:
        return html.Div([
            html.H6("No Statistics Available", className="text-muted"),
            html.P("Connect to data source to view performance metrics.")
        ])

    # If a cluster is selected, show cluster-specific statistics
    if selected_cluster and selected_cluster.get('cluster_id') is not None:
        return _create_cluster_specific_statistics(clustering_data, selected_cluster)

    # Otherwise show general performance statistics
    return _create_general_performance_statistics(clustering_data)


def _create_cluster_specific_statistics(clustering_data, selected_cluster):
    """Create cluster-specific statistics display"""
    try:
        logger.info(f"Creating cluster-specific statistics for cluster {selected_cluster.get('cluster_id')}")

        # Simple fallback for testing
        cluster_id = selected_cluster.get('cluster_id', 'Unknown')

        return html.Div([
            dbc.Card([
                dbc.CardHeader([
                    html.H5(f"Cluster {cluster_id} Selected", className="mb-0"),
                    dbc.Badge("Selected", color="primary", className="ms-2")
                ]),
                dbc.CardBody([
                    html.H6("Cluster Details", className="card-title"),
                    html.P(f"Cluster ID: {cluster_id}", className="mb-1"),
                    html.P("Detailed statistics will be displayed here.", className="mb-1"),
                    html.P("Click elsewhere to return to overview.", className="mb-0 text-muted small")
                ])
            ], className="mb-3")
        ])

        # Cluster Volatility Card
        if 'volatility' in current_state:
            volatility_data = current_state['volatility']
            cluster_volatilities = [volatility_data.get(symbol, 0) for symbol in stats['symbols']]

            if cluster_volatilities:
                avg_volatility = np.mean(cluster_volatilities)
                min_volatility = min(cluster_volatilities)
                max_volatility = max(cluster_volatilities)

                stats_cards.append(
                    dbc.Card([
                        dbc.CardBody([
                            html.H6("Cluster Volatility", className="card-title"),
                            html.P(f"Average Volatility: {avg_volatility:.4f}", className="mb-1"),
                            html.P(f"Volatility Range: {min_volatility:.4f} - {max_volatility:.4f}", className="mb-0")
                        ])
                    ], className="mb-3")
                )

        # Cluster Lifespan Card (placeholder for now)
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Cluster Stability", className="card-title"),
                    html.P("Lifespan tracking: Coming soon", className="mb-1 text-muted"),
                    html.P(f"Current stability: {current_state.get('regime_stability', 0):.1%}", className="mb-0")
                ])
            ], className="mb-3")
        )

        # Selection Info Card
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Selection Info", className="card-title"),
                    html.P(f"Selected from: {selected_cluster['source'].title()}", className="mb-1"),
                    html.P(f"Click anywhere to return to overview", className="mb-0 text-muted small")
                ])
            ], className="mb-3")
        )

        return html.Div(stats_cards)

    except Exception as e:
        logger.error(f"Error creating cluster-specific statistics: {str(e)}")
        return html.Div([
            html.H6("Statistics Error", className="text-danger"),
            html.P(f"Error loading cluster statistics: {str(e)[:100]}")
        ])


def _create_general_performance_statistics(clustering_data):
    """Create general performance statistics display"""
    try:
        status = clustering_data.get('status', {})
        current_state = clustering_data.get('current_state')

        # Create statistics cards
        stats_cards = []

        # Engine Statistics
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Engine Status", className="card-title"),
                    html.P(f"Status: {status.get('engine_status', 'Unknown')}", className="mb-1"),
                    html.P(f"Updates: {status.get('update_count', 0)}", className="mb-1"),
                    html.P(f"Errors: {status.get('error_count', 0)}", className="mb-1"),
                    html.P(f"Error Rate: {status.get('error_rate', 0):.1%}", className="mb-0")
                ])
            ], className="mb-3")
        )

        # Current State Statistics
        if current_state:
            stats_cards.append(
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Current State", className="card-title"),
                        html.P(f"Clusters: {current_state.get('cluster_count', 0)}", className="mb-1"),
                        html.P(f"Symbols: {len(current_state.get('symbols', []))}", className="mb-1"),
                        html.P(f"Data Quality: {current_state.get('data_quality_score', 0):.1%}", className="mb-1"),
                        html.P(f"Stability: {current_state.get('regime_stability', 0):.1%}", className="mb-0")
                    ])
                ], className="mb-3")
            )

        # Performance Metrics
        perf_metrics = status.get('performance_metrics', {})
        if perf_metrics:
            stats_cards.append(
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Performance", className="card-title"),
                        html.P(f"Total Updates: {perf_metrics.get('total_updates', 0)}", className="mb-1"),
                        html.P(f"Events Detected: {perf_metrics.get('events_detected', 0)}", className="mb-1"),
                        html.P(f"Avg Processing: {perf_metrics.get('average_processing_time', 0):.3f}s", className="mb-1"),
                        html.P(f"Uptime: {perf_metrics.get('uptime_hours', 0):.1f}h", className="mb-0")
                    ])
                ], className="mb-3")
            )

        # Recent Activity
        recent_events = clustering_data.get('recent_events', [])
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Recent Activity", className="card-title"),
                    html.P(f"Events (24h): {len(recent_events)}", className="mb-1"),
                    html.P(f"Last Update: {clustering_data.get('last_update', 'Never')}", className="mb-0")
                ])
            ], className="mb-3")
        )

        return html.Div(stats_cards)

    except Exception as e:
        logger.error(f"Error updating statistics panel: {str(e)}")
        return html.Div([
            html.H6("Statistics Error", className="text-danger"),
            html.P(f"Error: {str(e)[:100]}")
        ])


@app.callback(
    Output("selected-cluster-store", "data", allow_duplicate=True),
    [Input("statistics-panel", "n_clicks")],
    prevent_initial_call=True
)
def clear_cluster_selection(n_clicks):
    """Clear cluster selection when clicking on statistics panel"""
    if n_clicks:
        return None
    return dash.no_update


@app.callback(
    [Output("event-log-panel", "children"),
     Output("event-count-badge", "children")],
    [Input("clustering-data-store", "data")]
)
def update_event_log(clustering_data):
    """Update event log panel"""
    if not clustering_data:
        return html.Div([
            html.H6("No Events", className="text-muted"),
            html.P("Connect to data source to view market events.")
        ]), "0 Events"

    try:
        recent_events = clustering_data.get('recent_events', [])

        if not recent_events:
            return html.Div([
                html.H6("No Recent Events", className="text-muted"),
                html.P("No market regime changes detected in the selected timeframe.")
            ]), "0 Events"

        # Create event cards
        event_cards = []

        for event in recent_events[-10:]:  # Show last 10 events
            # Determine event color based on type
            event_colors = {
                'regime_change': 'danger',
                'volatility_spike': 'warning',
                'correlation_shift': 'info'
            }
            color = event_colors.get(event.get('event_type', ''), 'secondary')

            # Format timestamp
            timestamp = datetime.fromisoformat(event['timestamp'])
            time_str = timestamp.strftime('%H:%M:%S')
            date_str = timestamp.strftime('%Y-%m-%d')

            event_cards.append(
                dbc.Card([
                    dbc.CardBody([
                        html.Div([
                            dbc.Badge(event.get('event_type', 'unknown').replace('_', ' ').title(),
                                    color=color, className="me-2"),
                            html.Small(f"{date_str} {time_str}", className="text-muted float-end")
                        ], className="mb-2"),
                        html.P(event.get('description', 'No description'), className="mb-1"),
                        html.Small([
                            f"Significance: {event.get('significance_score', 0):.3f} | ",
                            f"Affected pairs: {len(event.get('affected_pairs', []))}"
                        ], className="text-muted")
                    ])
                ], className="mb-2")
            )

        event_log_content = html.Div(event_cards)
        event_count_text = f"{len(recent_events)} Events"

        return event_log_content, event_count_text

    except Exception as e:
        logger.error(f"Error updating event log: {str(e)}")
        return html.Div([
            html.H6("Event Log Error", className="text-danger"),
            html.P(f"Error: {str(e)[:100]}")
        ]), "Error"

if __name__ == "__main__":
    logger.info("Starting Dynamic FX Clustering Dashboard...")
    logger.info(f"Monitoring {len(CURRENCY_PAIRS)} currency pairs")
    logger.info(f"Data update interval: {DATA_UPDATE_INTERVAL/1000}s")
    logger.info(f"Chart update interval: {CHART_UPDATE_INTERVAL/1000}s")
    
    # Start the dashboard
    app.run(
        debug=True,
        host='127.0.0.1',
        port=8050,
        dev_tools_hot_reload=True
    )
