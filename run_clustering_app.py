"""
Dynamic FX Clustering Application - Main Dashboard
==================================================

Interactive 4-panel Dash dashboard for real-time forex market regime detection
with dendrogram visualization, Sankey diagrams, statistics panel, and event log.

Panels:
1. Top-Left: Interactive Dendrogram (cluster structure)
2. Top-Right: Sankey Diagram (cluster evolution over time)
3. Bottom-Left: Statistics Panel (metrics and performance)
4. Bottom-Right: Event Log (regime changes and alerts)
"""

import dash
from dash import dcc, html, Input, Output, State, callback_context, ALL
import dash_bootstrap_components as dbc
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import threading
import time
from typing import Dict, List, Optional, Any

# Import clustering components
from clustering.clustering_engine import ClusteringEngine
from clustering.state_manager import ClusteringState, ClusteringEvent
from clustering.dendrogram_utils import create_interactive_dendrogram, create_cluster_scatter_plot, calculate_cluster_statistics
from clustering.sankey_utils import (
    extract_cluster_evolution_data,
    create_cluster_evolution_sankey,
    create_cluster_timeline_chart,
    get_cluster_evolution_summary
)
from config import (
    CURRENCY_PAIRS, DATA_UPDATE_INTERVAL, CHART_UPDATE_INTERVAL,
    CHART_THEME, CHART_HEIGHT, CHART_WIDTH, MARKET_TIMEZONE
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import Rust clustering functions
try:
    import cluster_core
    RUST_AVAILABLE = True
    logger.info("Rust cluster_core module loaded successfully")
except ImportError as e:
    RUST_AVAILABLE = False
    logger.warning(f"Rust cluster_core module not available: {e}")
    logger.warning("Falling back to Python-only clustering")

# =============================================================================
# APPLICATION INITIALIZATION
# =============================================================================

# Initialize Dash app with Dark Bootstrap theme
app = dash.Dash(
    __name__,
    external_stylesheets=[dbc.themes.DARKLY, dbc.icons.FONT_AWESOME],
    title="Dynamic FX Clustering Dashboard",
    update_title="Updating..."
)

# Set Plotly dark theme globally
import plotly.io as pio
pio.templates.default = "plotly_dark"

# Custom CSS for dark theme dropdown
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <style>
            .Select-control {
                background-color: #495057 !important;
                border-color: #6c757d !important;
                color: white !important;
            }
            .Select-placeholder, .Select--single > .Select-control .Select-value {
                color: white !important;
            }
            .Select-menu-outer {
                background-color: #495057 !important;
                border-color: #6c757d !important;
            }
            .Select-option {
                background-color: #495057 !important;
                color: white !important;
            }
            .Select-option:hover {
                background-color: #6c757d !important;
                color: white !important;
            }
            .Select-option.is-selected {
                background-color: #17a2b8 !important;
                color: white !important;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# Initialize clustering engine
clustering_engine = ClusteringEngine(
    symbols=CURRENCY_PAIRS,
    event_threshold=0.7,
    min_data_quality=0.8,
    persistence_dir="data/clustering"
)

# Global state for dashboard
dashboard_state = {
    'last_update': None,
    'update_count': 0,
    'connection_status': 'disconnected',
    'auto_update': True,
    'selected_timeframe': '24h'
}

# Auto-connect on startup
logger.info("Attempting auto-connect to MT5...")
try:
    if clustering_engine.connect():
        dashboard_state['connection_status'] = 'connected'
        logger.info("Auto-connect successful")
    else:
        logger.warning("Auto-connect failed - user will need to connect manually")
except Exception as e:
    logger.error(f"Auto-connect error: {str(e)}")

# =============================================================================
# LAYOUT COMPONENTS
# =============================================================================

def create_header():
    """Create dashboard header with title and controls"""
    return dbc.Row([
        dbc.Col([
            html.H1("Dynamic FX Clustering Dashboard", className="text-primary mb-0"),
            html.P("Real-time forex market regime detection", className="text-muted")
        ], width=8),
        dbc.Col([
            dbc.ButtonGroup([
                dbc.Button("Connect", id="btn-connect", color="success", size="sm"),
                dbc.Button("Disconnect", id="btn-disconnect", color="danger", size="sm", disabled=True),
                dbc.Button("Refresh", id="btn-refresh", color="primary", size="sm")
            ], className="mb-2"),
            html.Div([
                dbc.Badge("Disconnected", id="status-badge", color="danger", className="me-2"),
                html.Small(id="last-update-text", className="text-muted")
            ])
        ], width=4, className="text-end")
    ], className="mb-4")

def create_control_panel():
    """Create control panel with timeframe and update settings"""
    return dbc.Card([
        dbc.CardBody([
            dbc.Row([
                dbc.Col([
                    html.Label("Timeframe:", className="form-label"),
                    dcc.Dropdown(
                        id="timeframe-dropdown",
                        options=[
                            {'label': 'Last 1 Hour', 'value': '1h'},
                            {'label': 'Last 6 Hours', 'value': '6h'},
                            {'label': 'Today (since 00:00)', 'value': '24h'},
                            {'label': 'Last 3 Days', 'value': '3d'},
                            {'label': 'Last Week', 'value': '7d'}
                        ],
                        value='24h',
                        clearable=False,
                        style={
                            'backgroundColor': '#495057',
                            'color': 'white'
                        }
                    )
                ], width=3),
                dbc.Col([
                    html.Label("Auto Update:", className="form-label"),
                    dbc.Switch(
                        id="auto-update-switch",
                        value=True,
                        className="mt-2"
                    )
                ], width=2),
                dbc.Col([
                    html.Label("Cluster Count:", className="form-label"),
                    html.H4(id="cluster-count-display", children="0", className="text-primary")
                ], width=2),
                dbc.Col([
                    html.Label("Data Quality:", className="form-label"),
                    html.H4(id="data-quality-display", children="0.0%", className="text-info")
                ], width=2),
                dbc.Col([
                    html.Label("Regime Stability:", className="form-label"),
                    html.H4(id="stability-display", children="0.0%", className="text-success")
                ], width=3)
            ])
        ])
    ], className="mb-3")

def create_main_panels():
    """Create the 4-panel main dashboard layout"""
    return html.Div([
        dbc.Row([
            # Top row
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Cluster Dendrogram", className="mb-0"),
                        dbc.Badge("Interactive", color="info", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="dendrogram-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Cluster Evolution", className="mb-0"),
                        dbc.Badge("Sankey Flow", color="warning", className="ms-2")
                    ]),
                    dbc.CardBody([
                        dcc.Loading(
                            dcc.Graph(
                                id="sankey-chart",
                                style={'height': f'{CHART_HEIGHT}px'},
                                config={'displayModeBar': True, 'displaylogo': False}
                            ),
                            type="circle"
                        )
                    ])
                ])
            ], width=6)
        ], className="mb-3"),
        dbc.Row([
            # Bottom row
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Performance Statistics", className="mb-0"),
                        dbc.Badge("Real-time", color="success", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="statistics-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5("Event Log", className="mb-0"),
                        dbc.Badge(id="event-count-badge", children="0 Events", color="secondary", className="ms-2")
                    ]),
                    dbc.CardBody([
                        html.Div(id="event-log-panel", style={'height': f'{CHART_HEIGHT}px', 'overflow-y': 'auto'})
                    ])
                ])
            ], width=6)
        ])
    ])

# =============================================================================
# MAIN LAYOUT
# =============================================================================

app.layout = dbc.Container([
    # Header
    create_header(),

    # Control Panel
    create_control_panel(),

    # Main Panels (Fixed function)
    create_main_panels(),

    # Event Comparison Modal
    dbc.Modal([
        dbc.ModalHeader(dbc.ModalTitle("Event Comparison: Before vs After")),
        dbc.ModalBody([
            html.Div(id="event-comparison-content")
        ]),
        dbc.ModalFooter([
            dbc.Button("Close", id="close-event-modal", className="ms-auto", n_clicks=0)
        ])
    ], id="event-comparison-modal", is_open=False, size="xl"),

    # Hidden components for data storage
    dcc.Store(id="clustering-data-store"),
    dcc.Store(id="dashboard-state-store", data=dashboard_state),
    dcc.Store(id="selected-cluster-store", data=None),
    dcc.Store(id="selected-event-store", data=None),


    # Interval components
    dcc.Interval(
        id="data-update-interval",
        interval=DATA_UPDATE_INTERVAL,
        n_intervals=0,
        disabled=False
    ),
    dcc.Interval(
        id="chart-update-interval",
        interval=CHART_UPDATE_INTERVAL,
        n_intervals=0,
        disabled=False
    )
], fluid=True)

# =============================================================================
# CALLBACK FUNCTIONS
# =============================================================================

@app.callback(
    [Output("status-badge", "children"),
     Output("status-badge", "color"),
     Output("btn-connect", "disabled"),
     Output("btn-disconnect", "disabled"),
     Output("last-update-text", "children")],
    [Input("btn-connect", "n_clicks"),
     Input("btn-disconnect", "n_clicks"),
     Input("data-update-interval", "n_intervals")],
    [State("dashboard-state-store", "data")]
)
def update_connection_status(connect_clicks, disconnect_clicks, n_intervals, state_data):
    """Update connection status and control buttons"""
    ctx = callback_context

    if not ctx.triggered:
        # Check actual connection status on initial load
        if dashboard_state['connection_status'] == 'connected':
            return "Connected", "success", True, False, "Auto-connected on startup"
        else:
            return "Disconnected", "danger", False, True, "Never updated"

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]

    if trigger_id == "btn-connect":
        try:
            if clustering_engine.connect():
                dashboard_state['connection_status'] = 'connected'
                return "Connected", "success", True, False, f"Connected at {datetime.now().strftime('%H:%M:%S')}"
            else:
                return "Connection Failed", "warning", False, True, "Connection attempt failed"
        except Exception as e:
            logger.error(f"Connection error: {str(e)}")
            return "Error", "danger", False, True, f"Error: {str(e)[:50]}"

    elif trigger_id == "btn-disconnect":
        try:
            clustering_engine.disconnect()
            dashboard_state['connection_status'] = 'disconnected'
            return "Disconnected", "danger", False, True, f"Disconnected at {datetime.now().strftime('%H:%M:%S')}"
        except Exception as e:
            logger.error(f"Disconnection error: {str(e)}")
            return "Error", "danger", False, True, f"Error: {str(e)[:50]}"

    # Regular status check
    if dashboard_state['connection_status'] == 'connected':
        last_update = dashboard_state.get('last_update')
        if last_update:
            return "Connected", "success", True, False, f"Last update: {last_update}"
        else:
            return "Connected", "success", True, False, "Connected - No data yet"
    else:
        return "Disconnected", "danger", False, True, "Not connected"


@app.callback(
    [Output("clustering-data-store", "data"),
     Output("cluster-count-display", "children"),
     Output("data-quality-display", "children"),
     Output("stability-display", "children")],
    [Input("data-update-interval", "n_intervals"),
     Input("btn-refresh", "n_clicks")],
    [State("auto-update-switch", "value"),
     State("timeframe-dropdown", "value")]
)
def update_clustering_data(n_intervals, refresh_clicks, auto_update, timeframe):
    """Fetch and update clustering data"""
    ctx = callback_context

    # Skip update if auto-update is disabled and not a manual refresh
    if not auto_update and not (ctx.triggered and 'btn-refresh' in ctx.triggered[0]['prop_id']):
        return dash.no_update, dash.no_update, dash.no_update, dash.no_update

    # Skip if not connected
    if dashboard_state['connection_status'] != 'connected':
        return {}, "0", "0.0%", "0.0%"

    try:
        # Parse timeframe - special handling for 24h (since 00:00)
        if timeframe == '24h':
            # Calculate hours since 00:00 today
            now = datetime.now(MARKET_TIMEZONE)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            timeframe_hours = int((now - today_start).total_seconds() / 3600)
            # Ensure minimum of 1 hour for data availability
            timeframe_hours = max(1, timeframe_hours)
        else:
            timeframe_hours = {
                '1h': 1, '6h': 6, '3d': 72, '7d': 168
            }.get(timeframe, 24)

        # Run clustering analysis
        event = clustering_engine.run_clustering_analysis(
            hours_back=timeframe_hours,
            use_weekend_fallback=True
        )

        # Generate historical states for Sankey if we don't have enough
        if len(clustering_engine.state_manager.get_state_history()) < 3:
            _generate_historical_states_for_demo(clustering_engine, timeframe_hours)

        # Get current status
        status = clustering_engine.get_current_status()
        current_state = clustering_engine.state_manager.get_current_state()

        # Update dashboard state
        dashboard_state['last_update'] = datetime.now().strftime('%H:%M:%S')
        dashboard_state['update_count'] += 1

        # Prepare data for storage
        clustering_data = {
            'status': status,
            'current_state': {
                'timestamp': current_state.timestamp.isoformat() if current_state else None,
                'cluster_count': current_state.cluster_count if current_state else 0,
                'cluster_assignments': current_state.cluster_assignments if current_state else [],
                'symbols': current_state.symbols if current_state else [],
                'regime_stability': current_state.regime_stability if current_state else 0.0,
                'data_quality_score': current_state.data_quality_score if current_state else 0.0,
                'correlation_matrix': current_state.correlation_matrix.tolist() if current_state and current_state.correlation_matrix is not None else []
            } if current_state else None,
            'recent_events': [
                {
                    'timestamp': event.timestamp.isoformat(),
                    'event_type': event.event_type,
                    'description': event.description,
                    'significance_score': event.significance_score,
                    'affected_pairs': event.affected_pairs
                }
                for event in clustering_engine.state_manager.get_recent_events(hours_back=timeframe_hours)
            ],
            'last_update': dashboard_state['last_update']
        }

        # Format display values
        cluster_count = current_state.cluster_count if current_state else 0
        data_quality = f"{(current_state.data_quality_score * 100):.1f}%" if current_state else "0.0%"
        stability = f"{(current_state.regime_stability * 100):.1f}%" if current_state else "0.0%"

        logger.info(f"Data updated: {cluster_count} clusters, quality={data_quality}, stability={stability}")

        return clustering_data, str(cluster_count), data_quality, stability

    except Exception as e:
        logger.error(f"Error updating clustering data: {str(e)}")
        return {}, "Error", "Error", "Error"


@app.callback(
    Output("dendrogram-chart", "figure"),
    [Input("clustering-data-store", "data"),
     Input("chart-update-interval", "n_intervals")]
)
def update_dendrogram(clustering_data, n_intervals):
    """Update dendrogram visualization with enhanced hierarchical clustering"""
    if not clustering_data or not clustering_data.get('current_state'):
        return create_cluster_scatter_plot(
            symbols=[],
            cluster_assignments=[],
            title="Dendrogram - No Data Available",
            height=CHART_HEIGHT
        )

    try:
        current_state = clustering_data['current_state']
        symbols = current_state.get('symbols', [])
        cluster_assignments = current_state.get('cluster_assignments', [])
        correlation_matrix = np.array(current_state.get('correlation_matrix', []))

        if len(symbols) == 0 or len(correlation_matrix) == 0:
            return create_cluster_scatter_plot(
                symbols=[],
                cluster_assignments=[],
                title="Dendrogram - Insufficient Data",
                height=CHART_HEIGHT
            )

        # Get linkage matrix if available from clustering engine
        linkage_matrix = None
        if RUST_AVAILABLE and hasattr(clustering_engine, 'last_linkage_matrix'):
            linkage_matrix = getattr(clustering_engine, 'last_linkage_matrix', None)

        # Try to create interactive dendrogram first
        try:
            if len(symbols) >= 3 and correlation_matrix.shape[0] >= 3:
                # Create true dendrogram visualization
                fig = create_interactive_dendrogram(
                    correlation_matrix=correlation_matrix,
                    symbols=symbols,
                    linkage_matrix=linkage_matrix,
                    cluster_assignments=cluster_assignments,
                    title=f"Currency Clustering Dendrogram ({len(set(cluster_assignments))} clusters)",
                    height=CHART_HEIGHT
                )

                # Add cluster statistics as annotations
                if len(cluster_assignments) > 0:
                    cluster_stats = calculate_cluster_statistics(symbols, cluster_assignments, correlation_matrix)
                    stats_text = []
                    for cluster_id, stats in cluster_stats.items():
                        stats_text.append(f"Cluster {cluster_id}: {stats['size']} pairs (avg corr: {stats['avg_correlation']:.2f})")

                    if stats_text:
                        fig.add_annotation(
                            text="<br>".join(stats_text[:5]),  # Show first 5 clusters
                            xref="paper", yref="paper",
                            x=0.02, y=0.98,
                            xanchor='left', yanchor='top',
                            showarrow=False,
                            font=dict(size=9, color="lightgray"),
                            bgcolor="rgba(0,0,0,0.5)",
                            bordercolor="gray",
                            borderwidth=1
                        )

                # Force remove legend
                fig.update_layout(showlegend=False)
                for trace in fig.data:
                    trace.showlegend = False
                return fig

        except Exception as dendrogram_error:
            logger.warning(f"Could not create dendrogram, falling back to scatter plot: {dendrogram_error}")

        # Fallback to cluster scatter plot
        fig = create_cluster_scatter_plot(
            symbols=symbols,
            cluster_assignments=cluster_assignments,
            correlation_matrix=correlation_matrix,
            title=f"Currency Pair Clusters ({len(set(cluster_assignments))} clusters)",
            height=CHART_HEIGHT
        )

        # Force remove legend
        fig.update_layout(showlegend=False)
        for trace in fig.data:
            trace.showlegend = False
        return fig

    except Exception as e:
        logger.error(f"Error creating dendrogram: {str(e)}")
        return create_cluster_scatter_plot(
            symbols=[],
            cluster_assignments=[],
            title=f"Dendrogram - Error: {str(e)[:50]}",
            height=CHART_HEIGHT
        )


@app.callback(
    Output("sankey-chart", "figure"),
    [Input("clustering-data-store", "data"),
     Input("timeframe-dropdown", "value")]
)
def update_sankey_diagram(clustering_data, timeframe):
    """Update Sankey diagram showing cluster evolution over time"""
    if not clustering_data:
        fig = go.Figure()
        fig.update_layout(
            title="Cluster Evolution - No Data Available",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color="white")
        )
        return fig

    try:
        # Get timeframe parameters - special handling for 24h (since 00:00)
        if timeframe == '24h':
            # Calculate hours since 00:00 today
            now = datetime.now(MARKET_TIMEZONE)
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            timeframe_hours = int((now - today_start).total_seconds() / 3600)
            # Ensure minimum of 1 hour for data availability
            timeframe_hours = max(1, timeframe_hours)
        else:
            timeframe_hours = {
                '1h': 1, '6h': 6, '3d': 72, '7d': 168
            }.get(timeframe, 24)

        # Get historical states from state manager
        state_history = clustering_engine.state_manager.get_state_history(limit=200)

        if len(state_history) < 3:
            # Not enough historical data - create informational chart
            fig = go.Figure()
            fig.add_annotation(
                text=f"Collecting data...<br>Need at least 3 states for evolution analysis<br>Current: {len(state_history)} states",
                xref="paper", yref="paper",
                x=0.5, y=0.5, xanchor='center', yanchor='middle',
                showarrow=False,
                font=dict(size=16, color="white"),
                bgcolor="rgba(255,255,255,0.1)",
                bordercolor="white",
                borderwidth=1
            )
            fig.update_layout(
                title=f"Cluster Evolution - Collecting Data ({timeframe})",
                template=CHART_THEME,
                height=CHART_HEIGHT,
                paper_bgcolor="rgba(0,0,0,0)",
                plot_bgcolor="rgba(0,0,0,0)",
                font=dict(color="white")
            )
            return fig

        # For Sankey evolution, always use the last several states regardless of timeframe
        # The historical states are generated with specific timestamps for demonstration
        recent_states = state_history[-10:]  # Always use last 10 states for evolution

        # Extract cluster evolution data
        # Use larger time windows to accommodate 6-hour historical state intervals
        time_window_minutes = max(120, timeframe_hours * 60 // 4)  # Larger windows for better grouping
        evolution_data = extract_cluster_evolution_data(
            recent_states,
            time_window_minutes=time_window_minutes,
            min_states=2
        )

        if evolution_data is None:
            # Fallback to timeline chart if Sankey data insufficient
            logger.info("Insufficient data for Sankey, creating timeline chart")
            fig = create_cluster_timeline_chart(
                {'time_windows': [{'window_start': s.timestamp, 'cluster_count': s.cluster_count,
                                  'avg_stability': s.regime_stability, 'avg_quality': s.data_quality_score}
                                 for s in recent_states]},
                title=f"Cluster Timeline ({timeframe})",
                height=CHART_HEIGHT
            )
            # Force remove legend
            fig.update_layout(showlegend=False)
            for trace in fig.data:
                trace.showlegend = False
            return fig

        # Create enhanced Sankey diagram
        fig = create_cluster_evolution_sankey(
            evolution_data,
            title=f"Currency Cluster Evolution ({timeframe})",
            height=CHART_HEIGHT,
            width=CHART_WIDTH
        )

        # Add evolution summary as annotation
        summary = get_cluster_evolution_summary(evolution_data)
        summary_text = (
            f"Windows: {summary.get('time_windows', 0)} | "
            f"Avg Clusters: {summary.get('avg_cluster_count', 0):.1f} | "
            f"Transitions: {summary.get('total_transitions', 0)}"
        )

        fig.add_annotation(
            text=summary_text,
            xref="paper", yref="paper",
            x=0.02, y=0.98, xanchor='left', yanchor='top',
            showarrow=False,
            font=dict(size=10, color="white"),
            bgcolor="rgba(0,0,0,0.7)",
            bordercolor="white",
            borderwidth=1
        )

        # Force remove legend (Sankey traces don't support showlegend property)
        fig.update_layout(showlegend=False)
        for trace in fig.data:
            if hasattr(trace, 'showlegend'):
                trace.showlegend = False
        return fig

    except Exception as e:
        logger.error(f"Error creating Sankey diagram: {str(e)}")
        fig = go.Figure()
        fig.update_layout(
            title=f"Cluster Evolution - Error: {str(e)[:50]}",
            template=CHART_THEME,
            height=CHART_HEIGHT,
            paper_bgcolor="rgba(0,0,0,0)",
            plot_bgcolor="rgba(0,0,0,0)",
            font=dict(color="white")
        )
        return fig


@app.callback(
    Output("selected-cluster-store", "data"),
    [Input("dendrogram-chart", "clickData"),
     Input("sankey-chart", "clickData")],
    [State("clustering-data-store", "data")]
)
def handle_cluster_selection(dendrogram_click, sankey_click, clustering_data):
    """Handle cluster selection from dendrogram or Sankey diagram clicks"""
    ctx = callback_context

    if not ctx.triggered or not clustering_data:
        logger.debug("No trigger or clustering data in cluster selection")
        return None

    trigger_id = ctx.triggered[0]['prop_id'].split('.')[0]
    logger.info(f"Cluster selection triggered by: {trigger_id}")
    logger.debug(f"Dendrogram click: {dendrogram_click}")
    logger.debug(f"Sankey click: {sankey_click}")

    try:
        selected_cluster = None

        if trigger_id == "dendrogram-chart" and dendrogram_click:
            # Extract cluster information from dendrogram click
            logger.info(f"Dendrogram click data: {dendrogram_click}")
            point_data = dendrogram_click['points'][0]
            logger.info(f"Point data: {point_data}")

            # For dendrogram, we need to map the clicked point to a cluster
            # This requires the current cluster assignments
            current_state = clustering_data.get('current_state', {})
            symbols = current_state.get('symbols', [])
            cluster_assignments = current_state.get('cluster_assignments', [])
            logger.info(f"Available symbols: {len(symbols)}, cluster assignments: {len(cluster_assignments)}")

            if 'text' in point_data:
                # Find the symbol that was clicked (from symbol scatter trace)
                clicked_symbol = point_data['text']
                logger.info(f"Clicked symbol: {clicked_symbol}")
                if clicked_symbol in symbols:
                    symbol_index = symbols.index(clicked_symbol)
                    if symbol_index < len(cluster_assignments):
                        cluster_id = cluster_assignments[symbol_index]
                        selected_cluster = {
                            'cluster_id': cluster_id,
                            'source': 'dendrogram',
                            'clicked_symbol': clicked_symbol
                        }
                        logger.info(f"Selected cluster: {selected_cluster}")
                    else:
                        logger.warning(f"Symbol index {symbol_index} out of range for cluster assignments")
                else:
                    logger.warning(f"Clicked symbol '{clicked_symbol}' not found in symbols list")
            elif 'x' in point_data:
                # Handle clicks on dendrogram tree structure
                x_pos = point_data['x']
                y_pos = point_data.get('y', 0)
                curve_number = point_data.get('curveNumber', -1)

                logger.info(f"Clicked on dendrogram at position ({x_pos}, {y_pos}), curve {curve_number}")

                # Check if this is a click on the symbol trace (bottom of dendrogram)
                if y_pos <= 0.1:  # Symbol trace is at y=0
                    # Map x position to symbol index for symbol trace clicks
                    n_symbols = len(symbols)
                    if n_symbols > 0 and 0 <= x_pos < n_symbols:
                        symbol_index = int(round(x_pos))
                        symbol_index = max(0, min(symbol_index, n_symbols - 1))

                        clicked_symbol = symbols[symbol_index]
                        if symbol_index < len(cluster_assignments):
                            cluster_id = cluster_assignments[symbol_index]
                            selected_cluster = {
                                'cluster_id': cluster_id,
                                'source': 'dendrogram',
                                'clicked_symbol': clicked_symbol
                            }
                            logger.info(f"Selected cluster {cluster_id} for symbol {clicked_symbol} at position {symbol_index}")
                        else:
                            logger.warning(f"Symbol index {symbol_index} out of range for cluster assignments")
                    else:
                        logger.warning(f"Invalid symbol position: {x_pos}")
                else:
                    # This is a click on a dendrogram node - select all symbols in that cluster
                    # For now, map to the nearest symbol and select its cluster
                    # TODO: Implement proper dendrogram node traversal for subtree selection
                    n_symbols = len(symbols)
                    if n_symbols > 0:
                        # Simple mapping for now - map x position to symbol range
                        symbol_index = int(round(x_pos * n_symbols / 270))  # 270 is typical dendrogram width
                        symbol_index = max(0, min(symbol_index, n_symbols - 1))

                        clicked_symbol = symbols[symbol_index]
                        if symbol_index < len(cluster_assignments):
                            cluster_id = cluster_assignments[symbol_index]
                            selected_cluster = {
                                'cluster_id': cluster_id,
                                'source': 'dendrogram_node',
                                'clicked_symbol': clicked_symbol,
                                'node_position': (x_pos, y_pos)
                            }
                            logger.info(f"Selected cluster {cluster_id} via dendrogram node at ({x_pos}, {y_pos})")
                        else:
                            logger.warning(f"Symbol index {symbol_index} out of range for cluster assignments")
                    else:
                        logger.warning(f"No symbols available for dendrogram node mapping")
            else:
                logger.warning(f"No 'text' or 'x' field in point data: {point_data.keys()}")

        elif trigger_id == "sankey-chart" and sankey_click:
            # Extract cluster information from Sankey click
            point_data = sankey_click['points'][0]

            # For Sankey, extract cluster ID from the node label
            if 'label' in point_data:
                label = point_data['label']
                # Parse cluster ID from label (format: "Cluster X (Y pairs)")
                import re
                cluster_match = re.search(r'Cluster (\d+)', label)
                if cluster_match:
                    selected_cluster = {
                        'cluster_id': int(cluster_match.group(1)),
                        'source': 'sankey',
                        'node_label': label
                    }

        return selected_cluster

    except Exception as e:
        logger.error(f"Error handling cluster selection: {str(e)}")
        return None


@app.callback(
    Output("statistics-panel", "children"),
    [Input("clustering-data-store", "data"),
     Input("selected-cluster-store", "data")]
)
def update_statistics_panel(clustering_data, selected_cluster):
    """Update statistics panel with cluster-specific or general performance statistics"""
    logger.info(f"Updating statistics panel - selected_cluster: {selected_cluster}")

    if not clustering_data:
        return html.Div([
            html.H6("No Statistics Available", className="text-muted"),
            html.P("Connect to data source to view performance metrics.")
        ])

    # If a cluster is selected, show cluster-specific statistics
    if selected_cluster and selected_cluster.get('cluster_id') is not None:
        logger.info(f"Showing cluster-specific statistics for cluster {selected_cluster.get('cluster_id')}")
        return _create_cluster_specific_statistics(clustering_data, selected_cluster)

    # Otherwise show general performance statistics
    logger.info("Showing general performance statistics")
    return _create_general_performance_statistics(clustering_data)


def _create_cluster_specific_statistics(clustering_data, selected_cluster):
    """Create cluster-specific statistics display"""
    try:
        logger.info(f"Creating cluster-specific statistics for cluster {selected_cluster.get('cluster_id')}")

        # Simple fallback for testing
        cluster_id = selected_cluster.get('cluster_id', 'Unknown')

        # Get cluster data from current state
        current_state = clustering_data.get('current_state')
        if not current_state:
            return html.Div([
                dbc.Card([
                    dbc.CardHeader([
                        html.H5(f"Cluster {cluster_id} Selected", className="mb-0 text-light"),
                        dbc.Badge("Selected", color="primary", className="ms-2")
                    ], style={'backgroundColor': '#2c3e50'}),
                    dbc.CardBody([
                        html.H6("No Data Available", className="card-title text-warning"),
                        html.P("Cluster data is not available.", className="mb-1 text-light"),
                        html.P("Click elsewhere to return to overview.", className="mb-0 text-muted small")
                    ], style={'backgroundColor': '#34495e', 'color': 'white'})
                ], className="mb-3", style={'border': '1px solid #495057'})
            ])

        # Extract cluster information
        symbols = current_state.get('symbols', [])
        cluster_assignments = current_state.get('cluster_assignments', [])
        correlation_matrix = current_state.get('correlation_matrix', [])

        # Calculate comprehensive cluster statistics
        import numpy as np
        cluster_stats = {}
        cluster_symbols = []

        if len(symbols) == len(cluster_assignments) and len(correlation_matrix) > 0:
            # Use the existing calculate_cluster_statistics function
            all_cluster_stats = calculate_cluster_statistics(symbols, cluster_assignments, np.array(correlation_matrix))
            cluster_stats = all_cluster_stats.get(cluster_id, {})
            cluster_symbols = cluster_stats.get('symbols', [])

        cluster_size = len(cluster_symbols)

        return html.Div([
            dbc.Card([
                dbc.CardHeader([
                    html.H5(f"Cluster {cluster_id} Details", className="mb-0 text-light"),
                    dbc.Badge(f"{cluster_size} pairs", color="info", className="ms-2")
                ], style={'backgroundColor': '#2c3e50'}),
                dbc.CardBody([
                    # Cluster Members Table
                    html.H6("Cluster Members", className="card-title text-light mb-3"),
                    dbc.Table([
                        html.Thead([
                            html.Tr([
                                html.Th("Currency Pair", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Th("Position", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                            ])
                        ]),
                        html.Tbody([
                            html.Tr([
                                html.Td(symbol, style={'color': 'white', 'fontWeight': '500'}),
                                html.Td(f"#{i+1}", style={'color': '#6c757d'}),
                            ]) for i, symbol in enumerate(cluster_symbols)
                        ])
                    ], bordered=True, hover=True, responsive=True,
                       style={'backgroundColor': '#495057', 'border': '1px solid #6c757d', 'color': 'white'}),

                    html.Hr(style={'borderColor': '#6c757d'}),

                    # Cluster Statistics
                    html.H6("Statistics", className="card-title text-light mb-3"),
                    dbc.Row([
                        dbc.Col([
                            dbc.Card([
                                dbc.CardBody([
                                    html.H4(str(cluster_size), className="text-info mb-0"),
                                    html.P("Currency Pairs", className="text-muted small mb-0")
                                ])
                            ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                        ], width=6),
                        dbc.Col([
                            dbc.Card([
                                dbc.CardBody([
                                    html.H4(f"{(cluster_size/len(symbols)*100):.1f}%", className="text-success mb-0"),
                                    html.P("of Total Pairs", className="text-muted small mb-0")
                                ])
                            ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                        ], width=6),
                    ], className="mb-3"),

                    # Correlation Statistics
                    _create_correlation_statistics_section(cluster_stats, cluster_size),

                    # Volatility Statistics (if available)
                    _create_volatility_statistics_section(current_state, cluster_symbols),

                    # Cluster Lifespan (placeholder for now)
                    _create_lifespan_statistics_section(current_state, cluster_id),

                    html.P("Click elsewhere to return to overview.", className="mb-0 text-muted small")
                ], style={'backgroundColor': '#34495e', 'color': 'white'})
            ], className="mb-3", style={'border': '1px solid #495057'})
        ])

        # Cluster Volatility Card
        if 'volatility' in current_state:
            volatility_data = current_state['volatility']
            cluster_volatilities = [volatility_data.get(symbol, 0) for symbol in stats['symbols']]

            if cluster_volatilities:
                avg_volatility = np.mean(cluster_volatilities)
                min_volatility = min(cluster_volatilities)
                max_volatility = max(cluster_volatilities)

                stats_cards.append(
                    dbc.Card([
                        dbc.CardBody([
                            html.H6("Cluster Volatility", className="card-title"),
                            html.P(f"Average Volatility: {avg_volatility:.4f}", className="mb-1"),
                            html.P(f"Volatility Range: {min_volatility:.4f} - {max_volatility:.4f}", className="mb-0")
                        ])
                    ], className="mb-3")
                )

        # Cluster Lifespan Card (placeholder for now)
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Cluster Stability", className="card-title"),
                    html.P("Lifespan tracking: Coming soon", className="mb-1 text-muted"),
                    html.P(f"Current stability: {current_state.get('regime_stability', 0):.1%}", className="mb-0")
                ])
            ], className="mb-3")
        )

        # Selection Info Card
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Selection Info", className="card-title"),
                    html.P(f"Selected from: {selected_cluster['source'].title()}", className="mb-1"),
                    html.P(f"Click anywhere to return to overview", className="mb-0 text-muted small")
                ])
            ], className="mb-3")
        )

        return html.Div(stats_cards)

    except Exception as e:
        logger.error(f"Error creating cluster-specific statistics: {str(e)}")
        return html.Div([
            html.H6("Statistics Error", className="text-danger"),
            html.P(f"Error loading cluster statistics: {str(e)[:100]}")
        ])


def _create_correlation_statistics_section(cluster_stats, cluster_size):
    """Create correlation statistics section with proper handling for single-member clusters."""
    if cluster_size <= 1:
        # Single member cluster - no meaningful correlation statistics
        return dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4("N/A", className="text-info mb-0"),
                        html.P("Single Member", className="text-muted small mb-0"),
                        html.P("No intra-cluster correlation for single-member clusters", className="text-muted small")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=12),
        ], className="mb-3")
    else:
        # Multi-member cluster - show correlation statistics
        avg_corr = cluster_stats.get('avg_correlation', 0)
        min_corr = cluster_stats.get('min_correlation', 0)
        max_corr = cluster_stats.get('max_correlation', 0)

        return dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4(f"{avg_corr:.3f}", className="text-warning mb-0"),
                        html.P("Avg Correlation", className="text-muted small mb-0")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=4),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4(f"{min_corr:.3f}", className="text-danger mb-0"),
                        html.P("Min Correlation", className="text-muted small mb-0")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=4),
            dbc.Col([
                dbc.Card([
                    dbc.CardBody([
                        html.H4(f"{max_corr:.3f}", className="text-success mb-0"),
                        html.P("Max Correlation", className="text-muted small mb-0")
                    ])
                ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
            ], width=4),
        ], className="mb-3")


def _create_volatility_statistics_section(current_state, cluster_symbols):
    """Create volatility statistics section for cluster"""
    try:
        volatility_profiles = current_state.get('volatility_profiles', {})

        if not volatility_profiles or not cluster_symbols:
            return html.Div()  # Return empty div if no data

        # Calculate cluster volatility statistics
        cluster_volatilities = []
        for symbol in cluster_symbols:
            if symbol in volatility_profiles:
                cluster_volatilities.append(volatility_profiles[symbol])

        if not cluster_volatilities:
            return html.Div()

        avg_volatility = np.mean(cluster_volatilities)
        min_volatility = min(cluster_volatilities)
        max_volatility = max(cluster_volatilities)

        return html.Div([
            html.H6("Volatility Analysis", className="card-title text-light mb-3"),
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{avg_volatility:.4f}", className="text-warning mb-0"),
                            html.P("Avg Volatility", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=4),
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{min_volatility:.4f}", className="text-success mb-0"),
                            html.P("Min Volatility", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=4),
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{max_volatility:.4f}", className="text-danger mb-0"),
                            html.P("Max Volatility", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=4),
            ], className="mb-3"),
        ])

    except Exception as e:
        logger.error(f"Error creating volatility statistics: {str(e)}")
        return html.Div()


def _create_lifespan_statistics_section(current_state, cluster_id):
    """Create lifespan statistics section for cluster"""
    try:
        # For now, show placeholder information
        # TODO: Implement proper cluster lifespan tracking
        regime_stability = current_state.get('regime_stability', 0.0)
        last_update = current_state.get('timestamp', 'Unknown')

        return html.Div([
            html.H6("Cluster Stability", className="card-title text-light mb-3"),
            dbc.Row([
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4(f"{regime_stability:.1%}", className="text-info mb-0"),
                            html.P("Stability Score", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=6),
                dbc.Col([
                    dbc.Card([
                        dbc.CardBody([
                            html.H4("Coming Soon", className="text-muted mb-0"),
                            html.P("Lifespan Tracking", className="text-muted small mb-0")
                        ])
                    ], style={'backgroundColor': '#495057', 'border': '1px solid #6c757d'})
                ], width=6),
            ], className="mb-3"),
        ])

    except Exception as e:
        logger.error(f"Error creating lifespan statistics: {str(e)}")
        return html.Div()


def _create_general_performance_statistics(clustering_data):
    """Create general performance statistics display"""
    try:
        status = clustering_data.get('status', {})
        current_state = clustering_data.get('current_state')

        # Create statistics cards
        stats_cards = []

        # Engine Statistics
        stats_cards.append(
            dbc.Card([
                dbc.CardHeader([
                    html.H6("Engine Status", className="mb-0 text-light")
                ], style={'backgroundColor': '#2c3e50'}),
                dbc.CardBody([
                    dbc.Table([
                        html.Tbody([
                            html.Tr([
                                html.Td("Status:", style={'color': '#17a2b8', 'fontWeight': 'bold', 'width': '40%'}),
                                html.Td(status.get('engine_status', 'Unknown'), style={'color': 'white'})
                            ]),
                            html.Tr([
                                html.Td("Updates:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Td(str(status.get('update_count', 0)), style={'color': 'white'})
                            ]),
                            html.Tr([
                                html.Td("Errors:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Td(str(status.get('error_count', 0)), style={'color': 'white'})
                            ]),
                            html.Tr([
                                html.Td("Error Rate:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                html.Td(f"{status.get('error_rate', 0):.1%}", style={'color': 'white'})
                            ])
                        ])
                    ], borderless=True, style={'marginBottom': '0'})
                ], style={'backgroundColor': '#34495e', 'color': 'white'})
            ], className="mb-3", style={'border': '1px solid #495057'})
        )

        # Current State Statistics
        if current_state:
            stats_cards.append(
                dbc.Card([
                    dbc.CardHeader([
                        html.H6("Current State", className="mb-0 text-light")
                    ], style={'backgroundColor': '#2c3e50'}),
                    dbc.CardBody([
                        dbc.Table([
                            html.Tbody([
                                html.Tr([
                                    html.Td("Clusters:", style={'color': '#17a2b8', 'fontWeight': 'bold', 'width': '40%'}),
                                    html.Td(str(current_state.get('cluster_count', 0)), style={'color': 'white'})
                                ]),
                                html.Tr([
                                    html.Td("Symbols:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                    html.Td(str(len(current_state.get('symbols', []))), style={'color': 'white'})
                                ]),
                                html.Tr([
                                    html.Td("Data Quality:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                    html.Td(f"{current_state.get('data_quality_score', 0):.1%}", style={'color': 'white'})
                                ]),
                                html.Tr([
                                    html.Td("Stability:", style={'color': '#17a2b8', 'fontWeight': 'bold'}),
                                    html.Td(f"{current_state.get('regime_stability', 0):.1%}", style={'color': 'white'})
                                ])
                            ])
                        ], borderless=True, style={'marginBottom': '0'})
                    ], style={'backgroundColor': '#34495e', 'color': 'white'})
                ], className="mb-3", style={'border': '1px solid #495057'})
            )

        # Performance Metrics
        perf_metrics = status.get('performance_metrics', {})
        if perf_metrics:
            stats_cards.append(
                dbc.Card([
                    dbc.CardBody([
                        html.H6("Performance", className="card-title"),
                        html.P(f"Total Updates: {perf_metrics.get('total_updates', 0)}", className="mb-1"),
                        html.P(f"Events Detected: {perf_metrics.get('events_detected', 0)}", className="mb-1"),
                        html.P(f"Avg Processing: {perf_metrics.get('average_processing_time', 0):.3f}s", className="mb-1"),
                        html.P(f"Uptime: {perf_metrics.get('uptime_hours', 0):.1f}h", className="mb-0")
                    ])
                ], className="mb-3")
            )

        # Recent Activity
        recent_events = clustering_data.get('recent_events', [])
        stats_cards.append(
            dbc.Card([
                dbc.CardBody([
                    html.H6("Recent Activity", className="card-title"),
                    html.P(f"Events (24h): {len(recent_events)}", className="mb-1"),
                    html.P(f"Last Update: {clustering_data.get('last_update', 'Never')}", className="mb-0")
                ])
            ], className="mb-3")
        )

        return html.Div(stats_cards)

    except Exception as e:
        logger.error(f"Error updating statistics panel: {str(e)}")
        return html.Div([
            html.H6("Statistics Error", className="text-danger"),
            html.P(f"Error: {str(e)[:100]}")
        ])


@app.callback(
    Output("selected-cluster-store", "data", allow_duplicate=True),
    [Input("statistics-panel", "n_clicks")],
    prevent_initial_call=True
)
def clear_cluster_selection(n_clicks):
    """Clear cluster selection when clicking on statistics panel"""
    if n_clicks:
        return None
    return dash.no_update


@app.callback(
    [Output("event-log-panel", "children"),
     Output("event-count-badge", "children")],
    [Input("clustering-data-store", "data")]
)
def update_event_log(clustering_data):
    """Update event log panel"""
    if not clustering_data:
        return html.Div([
            html.H6("No Events", className="text-muted"),
            html.P("Connect to data source to view market events.")
        ]), "0 Events"

    try:
        recent_events = clustering_data.get('recent_events', [])

        if not recent_events:
            return html.Div([
                html.H6("No Recent Events", className="text-muted"),
                html.P("No market regime changes detected in the selected timeframe.")
            ]), "0 Events"

        # Create event cards
        event_cards = []

        # Reverse order to show newest events first, then take last 10
        reversed_events = list(reversed(recent_events[-10:]))  # Show last 10 events, newest first
        for event_index, event in enumerate(reversed_events):
            # Determine event color based on type
            event_colors = {
                'regime_change': 'danger',
                'volatility_spike': 'warning',
                'correlation_shift': 'info'
            }
            color = event_colors.get(event.get('event_type', ''), 'secondary')

            # Format timestamp
            timestamp = datetime.fromisoformat(event['timestamp'])
            time_str = timestamp.strftime('%H:%M:%S')
            date_str = timestamp.strftime('%Y-%m-%d')

            event_cards.append(
                html.Div([
                    dbc.Card([
                        dbc.CardBody([
                            html.Div([
                                dbc.Badge(event.get('event_type', 'unknown').replace('_', ' ').title(),
                                        color=color, className="me-2"),
                                html.Small(f"{date_str} {time_str}", className="text-muted float-end")
                            ], className="mb-2"),
                            html.P(event.get('description', 'No description'), className="mb-1"),
                            html.Small(
                                f"Significance: {event.get('significance_score', 0):.3f} | "
                                f"Affected pairs: {len(event.get('affected_pairs', []))}",
                                className="text-muted"
                            ),
                            html.Small("Click to view before/after comparison", className="text-info d-block mt-1")
                        ])
                    ], className="mb-2")
                ], className="event-card-clickable", style={"cursor": "pointer"},
                   id={"type": "event-card", "index": event_index})
            )

        event_log_content = html.Div(event_cards)
        event_count_text = f"{len(recent_events)} Events"

        return event_log_content, event_count_text

    except Exception as e:
        logger.error(f"Error updating event log: {str(e)}")
        return html.Div([
            html.H6("Event Log Error", className="text-danger"),
            html.P(f"Error: {str(e)[:100]}")
        ]), "Error"




@app.callback(
    [Output("event-comparison-modal", "is_open"),
     Output("event-comparison-content", "children"),
     Output("selected-event-store", "data")],
    [Input({"type": "event-card", "index": ALL}, "n_clicks"),
     Input("close-event-modal", "n_clicks")],
    [State("event-comparison-modal", "is_open"),
     State("clustering-data-store", "data")]
)
def handle_event_comparison_modal(event_clicks, close_clicks, is_open, clustering_data):
    """Handle event card clicks and modal display"""
    ctx = callback_context

    if not ctx.triggered:
        return False, html.Div(), None

    trigger_id = ctx.triggered[0]['prop_id']
    logger.info(f"Event modal callback triggered: {trigger_id}, event_clicks: {event_clicks}")

    # Close modal
    if 'close-event-modal' in trigger_id:
        return False, html.Div(), None

    # Event card clicked via pattern-matching
    if 'event-card' in trigger_id and event_clicks:
        try:
            # Extract the clicked index from the trigger_id
            import json
            trigger_data = json.loads(trigger_id.split('.')[0])
            clicked_index = trigger_data.get('index')

            if clicked_index is None:
                return False, html.Div(), None

            logger.info(f"Processing event index: {clicked_index}")

            # Get event data
            recent_events = clustering_data.get('recent_events', [])
            if not recent_events:
                return False, html.Div("No events available"), None

            # Get the clicked event (accounting for reversed order in display)
            reversed_events = list(reversed(recent_events[-10:]))
            if clicked_index >= len(reversed_events):
                return False, html.Div("Event not found"), None

            selected_event = reversed_events[clicked_index]

            # Create before/after comparison content
            comparison_content = create_event_comparison_content(selected_event, clustering_data)

            return True, comparison_content, selected_event

        except Exception as e:
            logger.error(f"Error handling event comparison: {str(e)}")
            return False, html.Div(f"Error: {str(e)}"), None

    return is_open, dash.no_update, dash.no_update


def create_event_comparison_content(event, clustering_data):
    """Create before/after comparison content for an event"""
    try:
        # Event details
        timestamp = datetime.fromisoformat(event['timestamp'])
        event_type = event.get('event_type', 'unknown')
        description = event.get('description', 'No description')
        significance = event.get('significance_score', 0)
        affected_pairs = event.get('affected_pairs', [])

        # Create event summary
        event_summary = dbc.Card([
            dbc.CardHeader(html.H5(f"{event_type.replace('_', ' ').title()} Event")),
            dbc.CardBody([
                html.P(f"Time: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}", className="mb-1"),
                html.P(f"Description: {description}", className="mb-1"),
                html.P(f"Significance Score: {significance:.3f}", className="mb-1"),
                html.P(f"Affected Pairs: {len(affected_pairs)} pairs", className="mb-1"),
                html.Details([
                    html.Summary("Show affected pairs"),
                    html.P(", ".join(affected_pairs) if affected_pairs else "None specified")
                ])
            ])
        ], className="mb-3")

        # Create before/after comparison
        # For now, create placeholder content since we need to implement historical state retrieval
        before_after_content = dbc.Row([
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader(html.H6("Before Event")),
                    dbc.CardBody([
                        html.P("Cluster state before the event occurred", className="text-muted"),
                        html.Div("Historical dendrogram would be displayed here",
                               style={'height': '300px', 'border': '1px dashed #ccc',
                                     'display': 'flex', 'align-items': 'center',
                                     'justify-content': 'center', 'color': '#666'})
                    ])
                ])
            ], width=6),
            dbc.Col([
                dbc.Card([
                    dbc.CardHeader(html.H6("After Event")),
                    dbc.CardBody([
                        html.P("Cluster state after the event occurred", className="text-muted"),
                        html.Div("Current dendrogram would be displayed here",
                               style={'height': '300px', 'border': '1px dashed #ccc',
                                     'display': 'flex', 'align-items': 'center',
                                     'justify-content': 'center', 'color': '#666'})
                    ])
                ])
            ], width=6)
        ])

        # Statistical comparison
        stats_comparison = dbc.Card([
            dbc.CardHeader(html.H6("Statistical Changes")),
            dbc.CardBody([
                dbc.Row([
                    dbc.Col([
                        html.H6("Before", className="text-center"),
                        html.P("Clusters: N/A", className="mb-1"),
                        html.P("Quality: N/A", className="mb-1"),
                        html.P("Stability: N/A", className="mb-1")
                    ], width=4),
                    dbc.Col([
                        html.Div("→", className="text-center", style={'font-size': '2rem', 'color': '#007bff'})
                    ], width=4, className="d-flex align-items-center justify-content-center"),
                    dbc.Col([
                        html.H6("After", className="text-center"),
                        html.P("Clusters: N/A", className="mb-1"),
                        html.P("Quality: N/A", className="mb-1"),
                        html.P("Stability: N/A", className="mb-1")
                    ], width=4)
                ])
            ])
        ], className="mt-3")

        return html.Div([
            event_summary,
            before_after_content,
            stats_comparison
        ])

    except Exception as e:
        logger.error(f"Error creating event comparison content: {str(e)}")
        return html.Div([
            html.H5("Error", className="text-danger"),
            html.P(f"Could not create comparison: {str(e)}")
        ])


def _generate_historical_states_for_demo(clustering_engine, timeframe_hours):
    """Generate historical states from Friday's data for Sankey demonstration"""
    try:
        logger.info("Generating historical states for Sankey demonstration...")

        # Get Friday's data in 4-hour chunks to simulate evolution
        time_chunks = [6, 12, 18, 24]  # 6am, 12pm, 6pm, midnight
        base_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=24)

        for i, hours_back in enumerate(time_chunks):
            # Always generate all time chunks for Sankey evolution, regardless of timeframe
            # This ensures we have enough historical states for cluster evolution visualization
            clustering_engine.run_clustering_analysis(
                hours_back=hours_back,
                use_weekend_fallback=True
            )

            # Adjust the timestamp of the generated state to simulate historical progression
            if clustering_engine.state_manager.current_state:
                # Set timestamp to represent the actual time period
                simulated_time = base_time + timedelta(hours=i * 6)  # 6-hour intervals
                clustering_engine.state_manager.current_state.timestamp = simulated_time

                # Update the state in history with the new timestamp
                if clustering_engine.state_manager.state_history:
                    clustering_engine.state_manager.state_history[-1].timestamp = simulated_time

            logger.info(f"Generated historical state for {hours_back}h timeframe at {simulated_time}")

        logger.info(f"Generated {len(time_chunks)} historical states for Sankey")

    except Exception as e:
        logger.error(f"Error generating historical states: {str(e)}")


if __name__ == "__main__":
    logger.info("Starting Dynamic FX Clustering Dashboard...")
    logger.info(f"Monitoring {len(CURRENCY_PAIRS)} currency pairs")
    logger.info(f"Data update interval: {DATA_UPDATE_INTERVAL/1000}s")
    logger.info(f"Chart update interval: {CHART_UPDATE_INTERVAL/1000}s")
    
    # Start the dashboard
    app.run(
        debug=True,
        host='127.0.0.1',
        port=8050,
        dev_tools_hot_reload=True
    )
