# Project Blueprint: Dynamic FX Clustering Application (v2)

This document outlines a step-by-step plan for developing the Dynamic FX Clustering Application with a hybrid Python/Rust architecture, as defined in `spec.md`. The plan is structured as a series of prompts for a code-generation LLM, ensuring an incremental and logical development process.

## Phase 1: Rust Core Engine - Setup and Basic Data Processing

This phase focuses on creating the high-performance Rust library for core numerical computations.

### Prompt 1.1: Initialize Rust Project and Define Data Structures
```
Create a new Rust library project named `cluster_core` in a directory parallel to `clustering` (e.g., `cluster_core/`).

**Requirements:**
1.  Initialize a new Rust library: `cargo new cluster_core --lib`.
2.  Add `pyo3` and `ndarray` as dependencies in `Cargo.toml` for Python interoperability and numerical array handling.
3.  In `cluster_core/src/lib.rs`, define basic Rust structs to represent:
    -   `FxPriceData`: A struct to hold raw M1 price data (e.g., `Vec<f64>` for prices, `Vec<String>` for symbols, `Vec<i64>` for timestamps).
    -   `LogReturns`: A struct to hold computed log returns (`ndarray::Array2<f64>`).
    -   `CorrelationMatrix`: A struct to hold the correlation matrix (`ndarray::Array2<f64>`).
    -   `ClusteringResult`: A struct to hold cluster assignments (e.g., `Vec<i32>`), linkage matrix (if applicable), and associated metrics.
    -   `VolatilityProfile`: A struct to hold a 24-point daily volatility vector (`Vec<f64>`).
```

### Prompt 1.2: Implement Log Returns Calculation in Rust
```
In `cluster_core/src/lib.rs`, implement the `calculate_log_returns` function.

**Requirements:**
1.  This function should take `FxPriceData` as input.
2.  It should compute the minute-by-minute logarithmic returns for each currency pair.
3.  Return the results as an `LogReturns` struct.
4.  Ensure it can be exposed to Python via `pyo3`.
```

### Prompt 1.3: Implement Correlation Matrix Calculation in Rust
```
In `cluster_core/src/lib.rs`, implement the `compute_correlation_matrix` function.

**Requirements:**
1.  This function should take `LogReturns` as input.
2.  It should compute the Pearson correlation matrix for the given log returns.
3.  Return the result as a `CorrelationMatrix` struct.
4.  Ensure it can be exposed to Python via `pyo3`.
```

## Phase 2: Python Backend - Interfacing with Rust and Data Management

This phase updates the Python backend to utilize the Rust core and manages the application state.

### Prompt 2.1: Adapt `ClusteringDataManager` for Rust Interface
```
Modify `clustering/data_manager.py`.

**Requirements:**
1.  Import and reuse `MT5Connector` from `C:\Users\<USER>\Desktop\matrix_QP\mt5_connector.py`.
2.  Remove previous `calculate_log_returns` method (now handled by Rust).
3.  The `fetch_minute_data` method should now use the `MT5Connector` to fetch data and return raw price data in a format easily consumable by the Rust `FxPriceData` struct (e.g., NumPy arrays or lists that can be converted by `pyo3`).
4.  Add an `__init__` method to load the Rust library using `pyo3`'s `Python` module.
5.  Add a method `process_data_with_rust(self, raw_price_data)` that calls the Rust functions for log returns and correlation matrix calculation.
```

### Prompt 2.2: Update `ClusteringState` for Rust Integration
```
Modify `clustering/state_manager.py`.

**Requirements:**
1.  The `__init__` method should no longer initialize `HierarchicalClusterEngine` (its logic moves to Rust).
2.  The `update_state` method should:
    -   Call `ClusteringDataManager.fetch_minute_data`.
    -   Pass the raw data to `ClusteringDataManager.process_data_with_rust` to get the correlation matrix from Rust.
    -   The rest of the `update_state` (distance matrix, clustering, event detection) will be updated in subsequent Rust prompts.
3.  Ensure `self.history` stores the correlation matrix received from Rust.
```

## Phase 3: Rust Core Engine - Clustering Logic and Statistics

This phase expands the Rust core to perform clustering and calculate statistics.

### Prompt 3.1: Implement Hierarchical Clustering in Rust
```
In `cluster_core/src/lib.rs`, implement the `perform_hierarchical_clustering` function.

**Requirements:**
1.  This function should take a `CorrelationMatrix` as input.
2.  It should convert the correlation matrix into a condensed distance matrix.
3.  Perform hierarchical clustering (e.g., using a Rust-native clustering library or implementing a basic version).
4.  Return the linkage matrix and cluster assignments (given a distance threshold, or a list of possible thresholds) as part of a `ClusteringResult` struct.
5.  Ensure it can be exposed to Python via `pyo3`.
```

### Prompt 3.2: Implement Cluster Statistics Calculation in Rust
```
In `cluster_core/src/lib.rs`, implement the `calculate_cluster_statistics` function.

**Requirements:**
1.  This function should take `LogReturns`, current cluster assignments, and a list of symbols as input.
2.  For a given cluster ID, it should calculate:
    -   Member list (symbols).
    -   Average intra-cluster correlation.
    -   Overall cluster volatility (e.g., average standard deviation of log returns for members).
    -   (Stub for "lifespan" - can be tracked in Python).
3.  Return these statistics in a structured format.
4.  Ensure it can be exposed to Python via `pyo3`.
```

## Phase 4: Python Backend - Orchestration and State Management Refinement

This phase refines the Python backend to fully integrate the Rust core and manage the application state, including event detection and historical data.

### Prompt 4.1: Refactor `ClusteringState` for Full Rust Integration
```
Completely rewrite `clustering/state_manager.py`.

**Requirements:**
1.  `__init__`: Initialize the Rust core library.
2.  `update_state`:
    -   Fetch raw data via `ClusteringDataManager`.
    -   Call Rust functions to get: log returns, correlation matrix, linkage matrix, and current cluster assignments.
    -   Store these in `self.history` keyed by timestamp.
3.  `detect_events`:
    -   Now takes cluster assignments from the Rust output.
    -   Calculate Adjusted Rand Index (can be done in Python `sklearn` or Rust if preferred).
    -   Log events as before.
4.  `get_sankey_data`: Uses `self.history` and cluster assignments to generate Sankey data.
5.  `get_cluster_statistics`: Calls the Rust `calculate_cluster_statistics` function for a given cluster ID and timestamp.
```

### Prompt 4.2: Set Up the Dash Application Shell (4-Panel Layout)
```
Completely rewrite `run_clustering_app.py`.

**Requirements:**
1.  Import `dash`, `dash_core_components` as `dcc`, `dash_html_components` as `html`, `dash_bootstrap_components` as `dbc`, and `ClusteringState` from `clustering.state_manager`.
2.  Initialize the Dash app and `ClusteringState`.
3.  Define a new 4-panel layout using `dbc.Row` and `dbc.Col` components for:
    -   Top-Left: Dendrogram `html.Div`
    -   Top-Right: Sankey Diagram `html.Div`
    -   Bottom-Left: Statistics Panel `html.Div` (initially empty or placeholder)
    -   Bottom-Right: Event Log `html.Div`
4.  Include `dcc.Interval` for periodic updates.
5.  Create a callback triggered by `dcc.Interval` to call `state_manager.update_state()` and `state_manager.detect_events()`.
6.  Add the `if __name__ == '__main__':` block to run the server.
```

## Phase 5: Frontend - Visualization and Interactivity (Correlation Clustering)

This phase implements the UI components for the Correlation Clustering view.

### Prompt 5.1: Implement Dendrogram and Sankey Display
```
In `run_clustering_app.py`, update the callbacks for Dendrogram and Sankey.

**Requirements:**
1.  **Dendrogram:**
    -   Input: `n_intervals` and `dcc.Slider` value.
    -   Output: `children` of Dendrogram `html.Div`.
    -   Function: Retrieves linkage matrix and symbols from `ClusteringState` for the selected time, generates `plotly.figure_factory.create_dendrogram`.
2.  **Sankey Diagram:**
    -   Input: `n_intervals`.
    -   Output: `children` of Sankey `html.Div`.
    -   Function: Calls `state_manager.get_sankey_data()` and renders `go.Sankey`.
```

### Prompt 5.2: Implement Event Log and "Before and After" Modal
```
In `run_clustering_app.py`, update the callbacks for Event Log and the Modal.

**Requirements:**
1.  **Event Log:**
    -   Input: `n_intervals`.
    -   Output: `children` of Event Log `html.Div`.
    -   Function: Retrieves `event_log` from `ClusteringState`, formats into `html.Table` with clickable buttons.
2.  **"Before and After" Modal:**
    -   Input: Pattern-matching `Input` for event button clicks.
    -   Output: `is_open` and `children` of `dbc.Modal`.
    -   Function: Retrieves event details from `ClusteringState`, formats "Before and After" view, displays in modal.
```

### Prompt 5.3: Implement Cluster Statistics Panel
```
In `run_clustering_app.py`, implement the callback for the Cluster Statistics Panel.

**Requirements:**
1.  **Input:** A new `dcc.Store` component that holds the ID of the currently selected cluster (populated by a click on the dendrogram or Sankey diagram).
2.  **Output:** `children` of the Statistics Panel `html.Div`.
3.  The callback function should:
    -   If a cluster ID is provided, call `state_manager.get_cluster_statistics(cluster_id, current_timestamp)`.
    -   Format the returned statistics (member list, average correlation, average volatility, lifespan) into a clear display within the `html.Div`.
```

### Prompt 5.4: Implement Time Scrubber Slider
```
In `run_clustering_app.py`, update the Time Scrubber Slider.

**Requirements:**
1.  The slider's `min`, `max`, and `marks` should be dynamically updated by a callback based on available timestamps in `state_manager.history`.
2.  The `dendrogram` callback should use the slider's `value` to display historical data.
```

## Phase 6: Rust Core Engine - Volatility Clustering

This phase implements the volatility clustering logic in Rust.

### Prompt 6.1: Implement Daily Volatility Vector Calculation in Rust
```
In `cluster_core/src/lib.rs`, implement the `calculate_daily_volatility_vectors` function.

**Requirements:**
1.  This function should take historical M1 price data (for multiple days) as input.
2.  For each day, it calculates the 24-point hourly volatility vector across all currency pairs.
3.  Return these vectors in a structured format suitable for clustering.
4.  Ensure it can be exposed to Python via `pyo3`.
```

### Prompt 6.2: Implement Volatility Regime Clustering in Rust
```
In `cluster_core/src/lib.rs`, implement the `cluster_volatility_profiles` function.

**Requirements:**
1.  This function should take the daily volatility vectors as input.
2.  Use a clustering algorithm (e.g., K-Means implementation in Rust or a Rust library) to group these vectors into `n_clusters` regimes.
3.  Return the cluster assignments for each day and the "archetype" (centroid) for each regime.
4.  Ensure it can be exposed to Python via `pyo3`.
```

## Phase 7: Python Backend - Volatility Regime Orchestration

This phase integrates the Rust volatility clustering into the Python backend.

### Prompt 7.1: Enhance `ClusteringState` for Volatility Regimes
```
Modify `clustering/state_manager.py`.

**Requirements:**
1.  Add a new data store `self.volatility_regimes_history = {}` to store historical daily regime assignments and archetypes.
2.  Add a new method `update_volatility_regimes(self, num_days_history, n_clusters)` that:
    -   Calls Rust to calculate daily volatility vectors.
    -   Calls Rust to cluster these vectors and get assignments/archetypes.
    -   Stores the results in `self.volatility_regimes_history`.
3.  Add a method `get_intraday_regime_match(self, daily_volatility_vector)` that compares an hourly volatility vector to the stored archetypes and returns the closest regime label for each hour.
```

## Phase 8: Frontend - Volatility Regime UI

This phase implements the UI for the Volatility Regime Clustering view.

### Prompt 8.1: Implement Tabbed Layout and Regime Calendar
```
In `run_clustering_app.py`, implement the tabbed layout and the calendar view for volatility regimes.

**Requirements:**
1.  Add `dcc.Tabs` to the main layout for "Correlation Clustering" and "Volatility Regimes".
2.  The "Volatility Regimes" tab should contain the calendar UI.
3.  Create a callback that populates the calendar. It should:
    -   Retrieve historical daily regime assignments from `ClusteringState`.
    -   Color-code each day based on its regime.
    -   Make each day clickable.
```

### Prompt 8.2: Implement Daily Drill-Down View for Volatility
```
In `run_clustering_app.py`, implement the logic for clicking a day on the volatility calendar.

**Requirements:**
1.  Add a `dbc.Modal` for the daily drill-down.
2.  Create a callback triggered by clicking a calendar day.
3.  The callback should:
    -   Fetch the 24-hour volatility vector for the selected day.
    -   Call `state_manager.get_intraday_regime_match` to get hourly regime labels.
    -   Generate a Plotly line chart of the daily volatility, with annotations/colors indicating the matched intraday regimes.
    -   Display this chart in the modal.
```

## Phase 9: Finalization and Build Process

This phase focuses on integration, build processes, and documentation.

### Prompt 9.1: Python `setup.py` for Rust Integration
```
Create a `setup.py` file in the root directory to handle building the Rust library and making it importable by Python.

**Requirements:**
1.  Use `setuptools_rust` to build the `cluster_core` Rust library.
2.  Ensure `pip install .` in the root directory correctly builds the Rust library and installs the Python package.
```

### Prompt 9.2: Create Project Documentation
```
Create a new `README.md` file in the root directory for this new clustering project.

**Contents:**
1.  **Project Purpose:** A brief description of the application.
2.  **Architecture:** Explain the Python/Rust hybrid approach.
3.  **Setup Instructions:** How to install all dependencies (from `requirements.txt` and building the Rust core).
4.  **How to Run:** The command to start the application (`python run_clustering_app.py`).
5.  **UI Guide:** A brief overview of both the Correlation Clustering and Volatility Regimes dashboards and how to use them.
```

### Prompt 9.3: Update `requirements.txt`
```
Update the `requirements.txt` file in the root directory with all necessary Python dependencies, including `dash`, `plotly`, `pandas`, `numpy`, `scipy`, `scikit-learn`, `dash-bootstrap-components`, and `setuptools-rust`.