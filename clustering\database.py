"""
Database Layer for Dynamic FX Clustering Application

This module provides SQLite database functionality for persistent storage of:
- Clustering results and historical states
- Market events and regime changes
- Performance statistics and metrics
- Configuration and system data
"""

import sqlite3
import logging
import json
import pickle
import gzip
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
import numpy as np
from dataclasses import asdict

from clustering.state_manager import ClusteringState, ClusteringEvent, PerformanceMetrics
from config import MARKET_TIMEZONE

# Configure logging
logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    SQLite database manager for clustering application data persistence
    
    Handles:
    - Schema creation and migration
    - Clustering states and results storage
    - Event logging and retrieval
    - Performance metrics tracking
    - Data backup and restore
    """
    
    def __init__(self, db_path: str = "data/clustering.db"):
        """
        Initialize DatabaseManager
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Database connection
        self.connection: Optional[sqlite3.Connection] = None
        
        # Schema version for migrations
        self.schema_version = 1
        
        logger.info(f"DatabaseManager initialized with database: {self.db_path}")
    
    def connect(self) -> bool:
        """
        Connect to SQLite database and initialize schema
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            self.connection = sqlite3.connect(
                str(self.db_path),
                timeout=30.0,
                check_same_thread=False
            )
            
            # Enable foreign keys and WAL mode for better performance
            self.connection.execute("PRAGMA foreign_keys = ON")
            self.connection.execute("PRAGMA journal_mode = WAL")
            self.connection.execute("PRAGMA synchronous = NORMAL")
            
            # Initialize schema
            self._initialize_schema()
            
            logger.info("Database connection established successfully")
            return True
            
        except Exception as e:
            logger.error(f"Database connection failed: {str(e)}", exc_info=True)
            return False
    
    def disconnect(self):
        """Close database connection"""
        try:
            if self.connection:
                self.connection.close()
                self.connection = None
                logger.info("Database connection closed")
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")
    
    def _initialize_schema(self):
        """Initialize database schema"""
        try:
            cursor = self.connection.cursor()
            
            # Create clustering_states table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS clustering_states (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    correlation_matrix BLOB NOT NULL,
                    cluster_assignments TEXT NOT NULL,
                    cluster_count INTEGER NOT NULL,
                    symbols TEXT NOT NULL,
                    volatility_profiles TEXT NOT NULL,
                    regime_stability REAL NOT NULL,
                    data_quality_score REAL NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create clustering_events table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS clustering_events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    rand_index REAL NOT NULL,
                    significance_score REAL NOT NULL,
                    affected_pairs TEXT NOT NULL,
                    description TEXT NOT NULL,
                    previous_state_id INTEGER,
                    current_state_id INTEGER,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (previous_state_id) REFERENCES clustering_states(id),
                    FOREIGN KEY (current_state_id) REFERENCES clustering_states(id)
                )
            """)
            
            # Create performance_metrics table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    total_updates INTEGER NOT NULL,
                    events_detected INTEGER NOT NULL,
                    average_processing_time REAL NOT NULL,
                    data_quality_average REAL NOT NULL,
                    uptime_hours REAL NOT NULL,
                    error_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 1.0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create system_config table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_config (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    description TEXT,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indices for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_states_timestamp ON clustering_states(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_events_timestamp ON clustering_events(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_events_type ON clustering_events(event_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_metrics_timestamp ON performance_metrics(timestamp)")
            
            # Insert schema version
            cursor.execute("""
                INSERT OR REPLACE INTO system_config (key, value, description)
                VALUES ('schema_version', ?, 'Database schema version')
            """, (str(self.schema_version),))
            
            self.connection.commit()
            logger.info("Database schema initialized successfully")
            
        except Exception as e:
            logger.error(f"Schema initialization failed: {str(e)}", exc_info=True)
            raise
    
    def store_clustering_state(self, state: ClusteringState) -> Optional[int]:
        """
        Store clustering state in database
        
        Args:
            state: ClusteringState object to store
            
        Returns:
            Database ID of stored state, None if failed
        """
        try:
            cursor = self.connection.cursor()
            
            # Compress correlation matrix for storage
            correlation_blob = gzip.compress(pickle.dumps(state.correlation_matrix))
            
            cursor.execute("""
                INSERT INTO clustering_states (
                    timestamp, correlation_matrix, cluster_assignments, cluster_count,
                    symbols, volatility_profiles, regime_stability, data_quality_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                state.timestamp.isoformat(),
                correlation_blob,
                json.dumps(state.cluster_assignments),
                state.cluster_count,
                json.dumps(state.symbols),
                json.dumps(state.volatility_profiles),
                state.regime_stability,
                state.data_quality_score
            ))
            
            state_id = cursor.lastrowid
            self.connection.commit()
            
            logger.debug(f"Stored clustering state with ID: {state_id}")
            return state_id
            
        except Exception as e:
            logger.error(f"Error storing clustering state: {str(e)}", exc_info=True)
            return None
    
    def store_clustering_event(self, event: ClusteringEvent, 
                              previous_state_id: Optional[int] = None,
                              current_state_id: Optional[int] = None) -> Optional[int]:
        """
        Store clustering event in database
        
        Args:
            event: ClusteringEvent object to store
            previous_state_id: Database ID of previous state
            current_state_id: Database ID of current state
            
        Returns:
            Database ID of stored event, None if failed
        """
        try:
            cursor = self.connection.cursor()
            
            cursor.execute("""
                INSERT INTO clustering_events (
                    timestamp, event_type, rand_index, significance_score,
                    affected_pairs, description, previous_state_id, current_state_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                event.timestamp.isoformat(),
                event.event_type,
                event.rand_index,
                event.significance_score,
                json.dumps(event.affected_pairs),
                event.description,
                previous_state_id,
                current_state_id
            ))
            
            event_id = cursor.lastrowid
            self.connection.commit()
            
            logger.debug(f"Stored clustering event with ID: {event_id}")
            return event_id
            
        except Exception as e:
            logger.error(f"Error storing clustering event: {str(e)}", exc_info=True)
            return None
    
    def store_performance_metrics(self, metrics: PerformanceMetrics) -> Optional[int]:
        """
        Store performance metrics in database
        
        Args:
            metrics: PerformanceMetrics object to store
            
        Returns:
            Database ID of stored metrics, None if failed
        """
        try:
            cursor = self.connection.cursor()
            
            cursor.execute("""
                INSERT INTO performance_metrics (
                    timestamp, total_updates, events_detected, average_processing_time,
                    data_quality_average, uptime_hours
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                metrics.last_update.isoformat(),
                metrics.total_updates,
                metrics.events_detected,
                metrics.average_processing_time,
                metrics.data_quality_average,
                metrics.uptime_hours
            ))
            
            metrics_id = cursor.lastrowid
            self.connection.commit()
            
            logger.debug(f"Stored performance metrics with ID: {metrics_id}")
            return metrics_id
            
        except Exception as e:
            logger.error(f"Error storing performance metrics: {str(e)}", exc_info=True)
            return None

    def get_clustering_states(self,
                             start_time: Optional[datetime] = None,
                             end_time: Optional[datetime] = None,
                             limit: int = 100) -> List[ClusteringState]:
        """
        Retrieve clustering states from database

        Args:
            start_time: Start time filter
            end_time: End time filter
            limit: Maximum number of states to retrieve

        Returns:
            List of ClusteringState objects
        """
        try:
            cursor = self.connection.cursor()

            query = "SELECT * FROM clustering_states"
            params = []

            # Add time filters
            conditions = []
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time.isoformat())
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time.isoformat())

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            states = []
            for row in rows:
                # Decompress correlation matrix
                correlation_matrix = pickle.loads(gzip.decompress(row[2]))

                state = ClusteringState(
                    timestamp=datetime.fromisoformat(row[1]),
                    correlation_matrix=correlation_matrix,
                    cluster_assignments=json.loads(row[3]),
                    cluster_count=row[4],
                    symbols=json.loads(row[5]),
                    volatility_profiles=json.loads(row[6]),
                    regime_stability=row[7],
                    data_quality_score=row[8]
                )
                states.append(state)

            logger.debug(f"Retrieved {len(states)} clustering states")
            return states

        except Exception as e:
            logger.error(f"Error retrieving clustering states: {str(e)}", exc_info=True)
            return []

    def get_clustering_events(self,
                             start_time: Optional[datetime] = None,
                             end_time: Optional[datetime] = None,
                             event_type: Optional[str] = None,
                             limit: int = 100) -> List[ClusteringEvent]:
        """
        Retrieve clustering events from database

        Args:
            start_time: Start time filter
            end_time: End time filter
            event_type: Event type filter
            limit: Maximum number of events to retrieve

        Returns:
            List of ClusteringEvent objects
        """
        try:
            cursor = self.connection.cursor()

            query = "SELECT * FROM clustering_events"
            params = []

            # Add filters
            conditions = []
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time.isoformat())
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time.isoformat())
            if event_type:
                conditions.append("event_type = ?")
                params.append(event_type)

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            events = []
            for row in rows:
                event = ClusteringEvent(
                    timestamp=datetime.fromisoformat(row[1]),
                    event_type=row[2],
                    rand_index=row[3],
                    significance_score=row[4],
                    affected_pairs=json.loads(row[5]),
                    description=row[6]
                )
                events.append(event)

            logger.debug(f"Retrieved {len(events)} clustering events")
            return events

        except Exception as e:
            logger.error(f"Error retrieving clustering events: {str(e)}", exc_info=True)
            return []

    def get_performance_metrics_history(self,
                                       start_time: Optional[datetime] = None,
                                       end_time: Optional[datetime] = None,
                                       limit: int = 100) -> List[Dict[str, Any]]:
        """
        Retrieve performance metrics history from database

        Args:
            start_time: Start time filter
            end_time: End time filter
            limit: Maximum number of records to retrieve

        Returns:
            List of performance metrics dictionaries
        """
        try:
            cursor = self.connection.cursor()

            query = "SELECT * FROM performance_metrics"
            params = []

            # Add time filters
            conditions = []
            if start_time:
                conditions.append("timestamp >= ?")
                params.append(start_time.isoformat())
            if end_time:
                conditions.append("timestamp <= ?")
                params.append(end_time.isoformat())

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY timestamp DESC LIMIT ?"
            params.append(limit)

            cursor.execute(query, params)
            rows = cursor.fetchall()

            metrics_list = []
            for row in rows:
                metrics = {
                    'id': row[0],
                    'timestamp': datetime.fromisoformat(row[1]),
                    'total_updates': row[2],
                    'events_detected': row[3],
                    'average_processing_time': row[4],
                    'data_quality_average': row[5],
                    'uptime_hours': row[6],
                    'error_count': row[7] or 0,
                    'success_rate': row[8] or 1.0
                }
                metrics_list.append(metrics)

            logger.debug(f"Retrieved {len(metrics_list)} performance metrics records")
            return metrics_list

        except Exception as e:
            logger.error(f"Error retrieving performance metrics: {str(e)}", exc_info=True)
            return []

    def get_database_statistics(self) -> Dict[str, Any]:
        """
        Get database statistics and summary information

        Returns:
            Dictionary containing database statistics
        """
        try:
            cursor = self.connection.cursor()

            # Count records in each table
            cursor.execute("SELECT COUNT(*) FROM clustering_states")
            states_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM clustering_events")
            events_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM performance_metrics")
            metrics_count = cursor.fetchone()[0]

            # Get date ranges
            cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM clustering_states")
            states_range = cursor.fetchone()

            cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM clustering_events")
            events_range = cursor.fetchone()

            # Get database file size
            db_size = self.db_path.stat().st_size if self.db_path.exists() else 0

            # Get recent activity
            cursor.execute("""
                SELECT COUNT(*) FROM clustering_events
                WHERE timestamp >= datetime('now', '-24 hours')
            """)
            recent_events = cursor.fetchone()[0]

            cursor.execute("""
                SELECT COUNT(*) FROM clustering_states
                WHERE timestamp >= datetime('now', '-24 hours')
            """)
            recent_states = cursor.fetchone()[0]

            statistics = {
                'database_path': str(self.db_path),
                'database_size_bytes': db_size,
                'database_size_mb': round(db_size / (1024 * 1024), 2),
                'total_states': states_count,
                'total_events': events_count,
                'total_metrics': metrics_count,
                'states_date_range': {
                    'earliest': states_range[0] if states_range[0] else None,
                    'latest': states_range[1] if states_range[1] else None
                },
                'events_date_range': {
                    'earliest': events_range[0] if events_range[0] else None,
                    'latest': events_range[1] if events_range[1] else None
                },
                'recent_activity_24h': {
                    'events': recent_events,
                    'states': recent_states
                },
                'schema_version': self.schema_version
            }

            logger.debug("Database statistics retrieved successfully")
            return statistics

        except Exception as e:
            logger.error(f"Error retrieving database statistics: {str(e)}", exc_info=True)
            return {}

    def cleanup_old_data(self, days_to_keep: int = 30) -> Dict[str, int]:
        """
        Clean up old data from database

        Args:
            days_to_keep: Number of days of data to retain

        Returns:
            Dictionary with cleanup statistics
        """
        try:
            cursor = self.connection.cursor()
            cutoff_date = (datetime.now(MARKET_TIMEZONE) - timedelta(days=days_to_keep)).isoformat()

            # Count records to be deleted
            cursor.execute("SELECT COUNT(*) FROM clustering_states WHERE timestamp < ?", (cutoff_date,))
            states_to_delete = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM clustering_events WHERE timestamp < ?", (cutoff_date,))
            events_to_delete = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM performance_metrics WHERE timestamp < ?", (cutoff_date,))
            metrics_to_delete = cursor.fetchone()[0]

            # Delete old records
            cursor.execute("DELETE FROM clustering_states WHERE timestamp < ?", (cutoff_date,))
            cursor.execute("DELETE FROM clustering_events WHERE timestamp < ?", (cutoff_date,))
            cursor.execute("DELETE FROM performance_metrics WHERE timestamp < ?", (cutoff_date,))

            # Vacuum database to reclaim space
            cursor.execute("VACUUM")

            self.connection.commit()

            cleanup_stats = {
                'states_deleted': states_to_delete,
                'events_deleted': events_to_delete,
                'metrics_deleted': metrics_to_delete,
                'total_deleted': states_to_delete + events_to_delete + metrics_to_delete,
                'cutoff_date': cutoff_date,
                'days_kept': days_to_keep
            }

            logger.info(f"Database cleanup completed: {cleanup_stats['total_deleted']} records deleted")
            return cleanup_stats

        except Exception as e:
            logger.error(f"Error during database cleanup: {str(e)}", exc_info=True)
            return {}

    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """
        Create backup of database

        Args:
            backup_path: Path for backup file (auto-generated if None)

        Returns:
            True if backup successful, False otherwise
        """
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"data/backups/clustering_backup_{timestamp}.db"

            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)

            # Create backup using SQLite backup API
            backup_conn = sqlite3.connect(str(backup_file))
            self.connection.backup(backup_conn)
            backup_conn.close()

            logger.info(f"Database backup created: {backup_path}")
            return True

        except Exception as e:
            logger.error(f"Database backup failed: {str(e)}", exc_info=True)
            return False

    def restore_database(self, backup_path: str) -> bool:
        """
        Restore database from backup

        Args:
            backup_path: Path to backup file

        Returns:
            True if restore successful, False otherwise
        """
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                logger.error(f"Backup file not found: {backup_path}")
                return False

            # Close current connection
            if self.connection:
                self.connection.close()

            # Replace current database with backup
            backup_conn = sqlite3.connect(str(backup_file))
            self.connection = sqlite3.connect(str(self.db_path))
            backup_conn.backup(self.connection)
            backup_conn.close()

            # Reconnect and verify
            self.disconnect()
            success = self.connect()

            if success:
                logger.info(f"Database restored from backup: {backup_path}")
            else:
                logger.error("Database restore verification failed")

            return success

        except Exception as e:
            logger.error(f"Database restore failed: {str(e)}", exc_info=True)
            return False

    def execute_query(self, query: str, params: Tuple = ()) -> List[Tuple]:
        """
        Execute custom SQL query

        Args:
            query: SQL query string
            params: Query parameters

        Returns:
            List of result tuples
        """
        try:
            cursor = self.connection.cursor()
            cursor.execute(query, params)

            if query.strip().upper().startswith('SELECT'):
                results = cursor.fetchall()
                logger.debug(f"Query executed successfully, {len(results)} rows returned")
                return results
            else:
                self.connection.commit()
                logger.debug(f"Query executed successfully, {cursor.rowcount} rows affected")
                return []

        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}", exc_info=True)
            return []
