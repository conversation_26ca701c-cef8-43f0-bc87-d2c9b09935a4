"""
Test script for Enhanced Dendrogram Visualization
"""

import sys
import os
import numpy as np
from datetime import datetime

# Add clustering directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'clustering'))

def test_dendrogram_utilities():
    """Test dendrogram utility functions"""
    print("Testing Dendrogram Utilities")
    print("=" * 50)
    
    try:
        # Test imports
        print("1. Testing imports...")
        from clustering.dendrogram_utils import (
            create_interactive_dendrogram, 
            create_cluster_scatter_plot,
            calculate_cluster_statistics,
            correlation_to_distance_matrix
        )
        print("✓ Dendrogram utilities imported successfully")
        
        # Test correlation to distance conversion
        print("\n2. Testing correlation to distance conversion...")
        test_correlation = np.array([
            [1.0, 0.8, 0.3],
            [0.8, 1.0, 0.2],
            [0.3, 0.2, 1.0]
        ])
        distance_matrix = correlation_to_distance_matrix(test_correlation)
        print(f"✓ Distance matrix shape: {distance_matrix.shape}")
        print(f"  - Diagonal zeros: {np.allclose(np.diag(distance_matrix), 0)}")
        print(f"  - Symmetric: {np.allclose(distance_matrix, distance_matrix.T)}")
        
        # Test cluster scatter plot
        print("\n3. Testing cluster scatter plot...")
        test_symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD']
        test_assignments = [0, 0, 1, 1, 2]
        
        fig = create_cluster_scatter_plot(
            symbols=test_symbols,
            cluster_assignments=test_assignments,
            title="Test Cluster Visualization"
        )
        print(f"✓ Cluster scatter plot created with {len(fig.data)} traces")
        
        # Test cluster statistics
        print("\n4. Testing cluster statistics...")
        stats = calculate_cluster_statistics(
            symbols=test_symbols,
            cluster_assignments=test_assignments,
            correlation_matrix=test_correlation[:5, :5]  # Expand for 5 symbols
        )
        print(f"✓ Cluster statistics calculated for {len(stats)} clusters")
        for cluster_id, cluster_stats in stats.items():
            print(f"  - Cluster {cluster_id}: {cluster_stats['size']} symbols, avg corr: {cluster_stats['avg_correlation']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in dendrogram utilities test: {str(e)}")
        return False


def test_rust_clustering_integration():
    """Test Rust clustering integration"""
    print("\nTesting Rust Clustering Integration")
    print("=" * 50)
    
    try:
        # Test Rust core import
        print("1. Testing Rust core import...")
        try:
            import cluster_core
            print("✓ Rust cluster_core module available")
            rust_available = True
        except ImportError:
            print("⚠ Rust cluster_core module not available (expected in development)")
            rust_available = False
        
        if rust_available:
            # Test correlation to distance conversion
            print("\n2. Testing Rust correlation to distance conversion...")
            test_correlation = [
                [1.0, 0.8, 0.3, 0.1],
                [0.8, 1.0, 0.2, 0.4],
                [0.3, 0.2, 1.0, 0.6],
                [0.1, 0.4, 0.6, 1.0]
            ]
            
            distance_matrix = cluster_core.correlation_to_distance_matrix(test_correlation)
            print(f"✓ Rust distance matrix calculated: {len(distance_matrix)}x{len(distance_matrix[0])}")
            
            # Test hierarchical clustering
            print("\n3. Testing Rust hierarchical clustering...")
            linkage_matrix, cluster_assignments = cluster_core.perform_hierarchical_clustering(
                distance_matrix, "complete"
            )
            print(f"✓ Rust clustering completed:")
            print(f"  - Linkage matrix: {len(linkage_matrix)} merges")
            print(f"  - Cluster assignments: {cluster_assignments}")
            print(f"  - Number of clusters: {len(set(cluster_assignments))}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in Rust clustering test: {str(e)}")
        return False


def test_enhanced_clustering_engine():
    """Test enhanced clustering engine with linkage matrix storage"""
    print("\nTesting Enhanced Clustering Engine")
    print("=" * 50)
    
    try:
        # Test clustering engine with linkage storage
        print("1. Testing clustering engine initialization...")
        from clustering.clustering_engine import ClusteringEngine
        from config import CURRENCY_PAIRS
        
        engine = ClusteringEngine(
            symbols=CURRENCY_PAIRS[:5],  # Use subset for testing
            event_threshold=0.7,
            min_data_quality=0.8
        )
        print(f"✓ ClusteringEngine created with linkage storage")
        print(f"  - Symbols: {len(engine.symbols)}")
        print(f"  - Linkage matrix: {engine.last_linkage_matrix}")
        print(f"  - Distance matrix: {engine.last_distance_matrix}")
        
        # Test hierarchical clustering method
        print("\n2. Testing hierarchical clustering method...")
        test_correlation = np.random.rand(5, 5)
        test_correlation = (test_correlation + test_correlation.T) / 2
        np.fill_diagonal(test_correlation, 1.0)
        
        cluster_assignments, linkage_matrix = engine._perform_hierarchical_clustering(test_correlation)
        print(f"✓ Hierarchical clustering completed:")
        print(f"  - Cluster assignments: {cluster_assignments}")
        print(f"  - Linkage matrix available: {linkage_matrix is not None}")
        print(f"  - Stored in engine: {engine.last_linkage_matrix is not None}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in enhanced clustering engine test: {str(e)}")
        return False


def test_dashboard_dendrogram_integration():
    """Test dashboard dendrogram integration"""
    print("\nTesting Dashboard Dendrogram Integration")
    print("=" * 50)
    
    try:
        # Test dashboard dendrogram callback
        print("1. Testing dashboard dendrogram callback...")
        import run_clustering_app
        
        # Create mock clustering data
        mock_data = {
            'current_state': {
                'symbols': ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'],
                'cluster_assignments': [0, 0, 1, 1],
                'correlation_matrix': [
                    [1.0, 0.8, 0.3, 0.2],
                    [0.8, 1.0, 0.2, 0.3],
                    [0.3, 0.2, 1.0, 0.7],
                    [0.2, 0.3, 0.7, 1.0]
                ]
            }
        }
        
        # Test dendrogram update callback
        fig = run_clustering_app.update_dendrogram(mock_data, 1)
        print(f"✓ Dendrogram callback executed successfully")
        print(f"  - Figure type: {type(fig)}")
        print(f"  - Number of traces: {len(fig.data) if hasattr(fig, 'data') else 'N/A'}")
        print(f"  - Title: {fig.layout.title.text if hasattr(fig, 'layout') and hasattr(fig.layout, 'title') else 'N/A'}")
        
        # Test with empty data
        print("\n2. Testing with empty data...")
        empty_fig = run_clustering_app.update_dendrogram({}, 1)
        print(f"✓ Empty data handled gracefully")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in dashboard dendrogram integration test: {str(e)}")
        return False


def test_interactive_features():
    """Test interactive dendrogram features"""
    print("\nTesting Interactive Dendrogram Features")
    print("=" * 50)
    
    try:
        from clustering.dendrogram_utils import create_interactive_dendrogram
        
        # Create test data
        print("1. Creating test dendrogram with interactive features...")
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD']
        correlation_matrix = np.array([
            [1.0, 0.8, 0.3, 0.2, 0.1],
            [0.8, 1.0, 0.2, 0.3, 0.2],
            [0.3, 0.2, 1.0, 0.7, 0.4],
            [0.2, 0.3, 0.7, 1.0, 0.5],
            [0.1, 0.2, 0.4, 0.5, 1.0]
        ])
        cluster_assignments = [0, 0, 1, 1, 2]
        
        fig = create_interactive_dendrogram(
            correlation_matrix=correlation_matrix,
            symbols=symbols,
            cluster_assignments=cluster_assignments,
            title="Interactive Test Dendrogram"
        )
        
        print(f"✓ Interactive dendrogram created")
        print(f"  - Layout template: {fig.layout.template}")
        print(f"  - Height: {fig.layout.height}")
        print(f"  - Hover mode: {fig.layout.hovermode}")
        
        # Test with color threshold
        print("\n2. Testing with color threshold...")
        fig_colored = create_interactive_dendrogram(
            correlation_matrix=correlation_matrix,
            symbols=symbols,
            cluster_assignments=cluster_assignments,
            color_threshold=0.5,
            title="Color-Coded Dendrogram"
        )
        print(f"✓ Color-coded dendrogram created")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in interactive features test: {str(e)}")
        return False


if __name__ == "__main__":
    print("Enhanced Dendrogram Visualization Test Suite")
    print("=" * 70)
    
    # Run tests
    test1 = test_dendrogram_utilities()
    test2 = test_rust_clustering_integration()
    test3 = test_enhanced_clustering_engine()
    test4 = test_dashboard_dendrogram_integration()
    test5 = test_interactive_features()
    
    print("\n" + "=" * 70)
    print("TEST RESULTS:")
    print(f"✓ Dendrogram Utilities: {'PASSED' if test1 else 'FAILED'}")
    print(f"✓ Rust Integration: {'PASSED' if test2 else 'FAILED'}")
    print(f"✓ Enhanced Engine: {'PASSED' if test3 else 'FAILED'}")
    print(f"✓ Dashboard Integration: {'PASSED' if test4 else 'FAILED'}")
    print(f"✓ Interactive Features: {'PASSED' if test5 else 'FAILED'}")
    
    if all([test1, test2, test3, test4, test5]):
        print("\n🎉 All enhanced dendrogram tests PASSED!")
        print("\nEnhanced features available:")
        print("- Interactive hierarchical clustering dendrogram")
        print("- Rust-powered clustering with linkage matrix")
        print("- Cluster statistics and correlation analysis")
        print("- Fallback to scatter plot visualization")
        print("- Enhanced hover information and interactivity")
    else:
        print("\n❌ Some tests FAILED - check errors above")
