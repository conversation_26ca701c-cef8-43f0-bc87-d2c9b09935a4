{"rustc": 1842507548689473721, "features": "[\"approx\", \"default\", \"rayon\", \"rayon_\", \"serde\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 2040997289075261528, "path": 2525462169438355074, "deps": [[2289341005599476083, "approx", false, 14167278476402842237], [5157631553186200874, "num_traits", false, 8484145598454306592], [9689903380558560274, "serde", false, 1343160699224734967], [10697383615564341592, "rayon_", false, 10694343906854033479], [12319020793864570031, "num_complex", false, 9921354129542358373], [15709748443193639506, "rawpointer", false, 18169895621523116006], [15826188163127377936, "matrixmultiply", false, 16907958943124577366], [16795989132585092538, "num_integer", false, 5058628175493339097]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\ndarray-da20bbfc0634953e\\dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}