{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"default\", \"extension-module\", \"generate-import-lib\"]", "target": 14506753996192664611, "profile": 7086611809595859799, "path": 3159355557301610315, "deps": [[46745629712228035, "build_script_build", false, 15221801820267761112], [4684437522915235464, "libc", false, 9685844023081248841]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-ffi-8bec400a41e9bc33\\dep-lib-pyo3_ffi", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}