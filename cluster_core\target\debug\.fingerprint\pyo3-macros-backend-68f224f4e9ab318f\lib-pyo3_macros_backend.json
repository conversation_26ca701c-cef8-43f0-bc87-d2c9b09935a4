{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"experimental-async\", \"gil-refs\"]", "target": 1500063600279316151, "profile": 7218910627595408246, "path": 6343010332306757584, "deps": [[3060637413840920116, "proc_macro2", false, 9742792036988073286], [3771343242488894766, "build_script_build", false, 3884457880194102154], [4974441333307933176, "syn", false, 7602834080497375398], [9343146279897821472, "pyo3_build_config", false, 6161889212068654936], [13077543566650298139, "heck", false, 8749295945489699306], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-macros-backend-68f224f4e9ab318f\\dep-lib-pyo3_macros_backend", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}