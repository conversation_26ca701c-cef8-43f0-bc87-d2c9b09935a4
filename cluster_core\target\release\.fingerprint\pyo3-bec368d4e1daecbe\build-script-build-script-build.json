{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"gil-refs\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 5408242616063297496, "profile": 2698605196217230458, "path": 10447564592087428371, "deps": [[9343146279897821472, "pyo3_build_config", false, 8028591707671156473]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pyo3-bec368d4e1daecbe\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}