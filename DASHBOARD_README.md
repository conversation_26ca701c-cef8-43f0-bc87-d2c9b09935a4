# Dynamic FX Clustering Dashboard

## Overview

Interactive 4-panel Dash dashboard for real-time forex market regime detection with dendrogram visualization, Sankey diagrams, statistics panel, and event log.

## Features

### 📊 **4-Panel Layout**
1. **Top-Left: Interactive Dendrogram** - Cluster structure visualization
2. **Top-Right: Sankey Diagram** - Cluster evolution over time  
3. **Bottom-Left: Statistics Panel** - Performance metrics and system status
4. **Bottom-Right: Event Log** - Market regime changes and alerts

### 🔄 **Real-time Updates**
- Automatic data refresh every 5 minutes
- Chart updates every 1 minute
- Manual refresh capability
- Connection status monitoring

### ⚙️ **Interactive Controls**
- **Connect/Disconnect**: MT5 data source control
- **Timeframe Selection**: 1h, 6h, 24h, 3d, 7d analysis windows
- **Auto-Update Toggle**: Enable/disable automatic refreshing
- **Real-time Metrics**: Cluster count, data quality, regime stability

## Quick Start

### 1. Start the Dashboard
```bash
python run_clustering_app.py
```

### 2. Open in Browser
Navigate to: http://127.0.0.1:8050

### 3. Connect to Data Source
1. Click the **"Connect"** button in the header
2. Wait for connection status to show "Connected"
3. Data will begin updating automatically

## Dashboard Components

### Header Controls
- **Connection Status**: Shows current MT5 connection state
- **Connect/Disconnect**: Control data source connection
- **Refresh**: Manual data update trigger
- **Last Update**: Timestamp of most recent data refresh

### Control Panel
- **Timeframe Dropdown**: Select analysis window (1h to 7d)
- **Auto Update Switch**: Toggle automatic data refresh
- **Live Metrics**: Current cluster count, data quality, stability

### Panel Details

#### 1. Cluster Dendrogram
- **Purpose**: Visualize current currency pair clustering structure
- **Features**: Interactive scatter plot showing cluster assignments
- **Colors**: Each cluster has distinct color coding
- **Hover**: Shows currency pair and cluster information

#### 2. Cluster Evolution (Sankey)
- **Purpose**: Show how clusters change over time
- **Features**: Flow diagram of cluster transitions
- **Timeframe**: Adjustable via control panel
- **Note**: Currently shows placeholder data (to be enhanced)

#### 3. Performance Statistics
- **Engine Status**: Connection and processing state
- **Current State**: Active cluster information
- **Performance Metrics**: Processing times and system health
- **Recent Activity**: Event counts and update timestamps

#### 4. Event Log
- **Recent Events**: Market regime changes and alerts
- **Event Types**: 
  - 🔴 Regime Change (cluster structure shifts)
  - 🟡 Volatility Spike (market stress events)
  - 🔵 Correlation Shift (relationship changes)
- **Details**: Timestamp, significance score, affected pairs

## Configuration

### Update Intervals
- **Data Update**: 5 minutes (300,000ms)
- **Chart Update**: 1 minute (60,000ms)
- **Configurable**: Edit `config.py` to adjust

### Styling
- **Theme**: Dark theme (plotly_dark)
- **Bootstrap**: Responsive design with Bootstrap components
- **Height**: 500px charts (configurable)

### Currency Pairs
- **Default**: All 28 major forex pairs
- **Configurable**: Edit `CURRENCY_PAIRS` in `config.py`

## Testing

### Run Dashboard Tests
```bash
python test_dashboard.py
```

### Test Components
- ✅ Component initialization
- ✅ Data flow validation  
- ✅ Mock data processing
- ✅ Layout rendering

## Architecture

### Data Flow
1. **ClusteringEngine** fetches MT5 data
2. **Rust Core** performs clustering analysis
3. **StateManager** tracks regime changes
4. **Dashboard** visualizes results in real-time

### Key Files
- `run_clustering_app.py` - Main dashboard application
- `clustering/clustering_engine.py` - Data processing engine
- `clustering/state_manager.py` - State and event management
- `clustering/database.py` - Persistent storage layer
- `config.py` - Configuration settings

## Dependencies

### Required Packages
- `dash` - Web dashboard framework
- `dash-bootstrap-components` - UI components
- `plotly` - Interactive visualizations
- `pandas` - Data manipulation
- `numpy` - Numerical computing

### Clustering Components
- Custom Rust core engine (cluster_core)
- MT5 data manager
- State management system
- Database persistence layer

## Troubleshooting

### Connection Issues
- Ensure MT5 terminal is running and logged in
- Check MT5 algorithm trading is enabled
- Verify network connectivity

### Performance Issues
- Reduce timeframe window for faster updates
- Disable auto-update during heavy analysis
- Check system resources and memory usage

### Data Issues
- Weekend data automatically falls back to Friday
- Missing data is handled gracefully
- Quality scores indicate data completeness

## Next Steps

### Planned Enhancements
1. **Enhanced Dendrogram**: True hierarchical clustering visualization
2. **Real Sankey Flows**: Historical cluster evolution tracking
3. **Advanced Analytics**: Volatility regime clustering
4. **Alert System**: Real-time notifications
5. **Export Features**: Data and chart export capabilities

### Development Status
- ✅ **Phase 1**: Rust Core Engine (Complete)
- ✅ **Phase 2**: Python Backend Integration (Complete)  
- 🔄 **Phase 3**: Frontend Dashboard Development (In Progress)
- ⏳ **Phase 4**: Advanced Features and Optimization
- ⏳ **Phase 5**: Testing and Deployment
