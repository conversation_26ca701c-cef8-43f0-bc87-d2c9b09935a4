"""
State Management System for Dynamic FX Clustering Application

This module provides centralized state management for:
- Clustering results and historical data
- Event detection using Adjusted Rand Index
- Real-time state updates and persistence
- Performance metrics and statistics
"""

import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from collections import deque
import numpy as np
from sklearn.metrics import adjusted_rand_score
import json
import pickle
from pathlib import Path

from config import MARKET_TIMEZONE

# Import database manager (optional dependency)
try:
    from .database import DatabaseManager
    DATABASE_AVAILABLE = True
except ImportError as e1:
    try:
        # Try absolute import as fallback
        from clustering.database import DatabaseManager
        DATABASE_AVAILABLE = True
    except ImportError as e2:
        try:
            # Try direct import
            import sys
            import os
            sys.path.append(os.path.dirname(__file__))
            from database import DatabaseManager
            DATABASE_AVAILABLE = True
        except ImportError as e3:
            DATABASE_AVAILABLE = False
            DatabaseManager = None
            # Uncomment for debugging: print(f"Database import failed: {e1}, {e2}, {e3}")

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class ClusteringState:
    """Current clustering state snapshot"""
    timestamp: datetime
    correlation_matrix: np.ndarray
    cluster_assignments: List[int]
    cluster_count: int
    symbols: List[str]
    volatility_profiles: Dict[str, float]
    regime_stability: float
    data_quality_score: float


@dataclass
class ClusteringEvent:
    """Detected clustering regime change event"""
    timestamp: datetime
    event_type: str  # 'regime_change', 'volatility_spike', 'correlation_shift'
    previous_state: ClusteringState
    current_state: ClusteringState
    rand_index: float
    significance_score: float
    affected_pairs: List[str]
    description: str


@dataclass
class PerformanceMetrics:
    """System performance and statistics"""
    total_updates: int
    events_detected: int
    average_processing_time: float
    data_quality_average: float
    uptime_hours: float
    last_update: datetime


class StateManager:
    """
    Centralized state management for clustering application
    
    Handles:
    - Current and historical clustering states
    - Event detection using Adjusted Rand Index
    - Performance monitoring and metrics
    - State persistence and recovery
    """
    
    def __init__(self,
                 max_history_size: int = 1000,
                 event_threshold: float = 0.7,
                 persistence_dir: str = "data/state",
                 use_database: bool = True,
                 db_path: str = "data/clustering.db"):
        """
        Initialize StateManager

        Args:
            max_history_size: Maximum number of historical states to keep
            event_threshold: ARI threshold for detecting regime changes
            persistence_dir: Directory for state persistence
            use_database: Whether to use database for persistence
            db_path: Path to SQLite database file
        """
        self.max_history_size = max_history_size
        self.event_threshold = event_threshold
        self.persistence_dir = Path(persistence_dir)
        self.persistence_dir.mkdir(parents=True, exist_ok=True)

        # Database integration
        self.use_database = use_database and DATABASE_AVAILABLE
        self.db_manager: Optional[DatabaseManager] = None

        if self.use_database:
            try:
                self.db_manager = DatabaseManager(db_path)
                if self.db_manager.connect():
                    logger.info("StateManager: Database integration enabled")
                else:
                    logger.warning("StateManager: Database connection failed, using file persistence")
                    self.use_database = False
                    self.db_manager = None
            except Exception as e:
                logger.error(f"StateManager: Database initialization failed: {str(e)}")
                self.use_database = False
                self.db_manager = None
        else:
            logger.info("StateManager: Using file-based persistence")
        
        # Current state
        self.current_state: Optional[ClusteringState] = None
        self.previous_state: Optional[ClusteringState] = None
        
        # Historical data
        self.state_history: deque = deque(maxlen=max_history_size)
        self.event_history: deque = deque(maxlen=max_history_size)
        
        # Performance tracking
        self.start_time = datetime.now(MARKET_TIMEZONE)
        self.processing_times: deque = deque(maxlen=100)
        self.update_count = 0
        self.event_count = 0
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Load persisted state if available
        self._load_persisted_state()
        
        logger.info("StateManager initialized")
    
    def update_state(self, 
                     correlation_matrix: np.ndarray,
                     cluster_assignments: List[int],
                     symbols: List[str],
                     volatility_profiles: Dict[str, float],
                     data_quality_score: float = 1.0) -> Optional[ClusteringEvent]:
        """
        Update current clustering state and detect events
        
        Args:
            correlation_matrix: Current correlation matrix
            cluster_assignments: Cluster assignments for each symbol
            symbols: List of currency pair symbols
            volatility_profiles: Volatility data for each symbol
            data_quality_score: Quality score of input data (0-1)
            
        Returns:
            ClusteringEvent if regime change detected, None otherwise
        """
        start_time = time.time()
        
        with self.lock:
            try:
                # Create new state
                new_state = ClusteringState(
                    timestamp=datetime.now(MARKET_TIMEZONE),
                    correlation_matrix=correlation_matrix.copy(),
                    cluster_assignments=cluster_assignments.copy(),
                    cluster_count=len(set(cluster_assignments)),
                    symbols=symbols.copy(),
                    volatility_profiles=volatility_profiles.copy(),
                    regime_stability=self._calculate_regime_stability(cluster_assignments),
                    data_quality_score=data_quality_score
                )
                
                # Detect events if we have previous state
                event = None
                if self.current_state is not None:
                    event = self._detect_regime_change(self.current_state, new_state)
                
                # Update states
                self.previous_state = self.current_state
                self.current_state = new_state
                
                # Add to history
                self.state_history.append(new_state)
                if event:
                    self.event_history.append(event)
                    self.event_count += 1
                
                # Update performance metrics
                processing_time = time.time() - start_time
                self.processing_times.append(processing_time)
                self.update_count += 1
                
                # Persist state periodically
                if self.update_count % 10 == 0:
                    self._persist_state()
                
                logger.info(f"State updated: {new_state.cluster_count} clusters, "
                           f"quality={data_quality_score:.3f}, "
                           f"stability={new_state.regime_stability:.3f}")
                
                if event:
                    logger.warning(f"Regime change detected: {event.event_type} "
                                 f"(ARI={event.rand_index:.3f})")
                
                return event
                
            except Exception as e:
                logger.error(f"Error updating state: {str(e)}", exc_info=True)
                return None
    
    def _detect_regime_change(self, 
                             previous: ClusteringState, 
                             current: ClusteringState) -> Optional[ClusteringEvent]:
        """
        Detect regime changes using Adjusted Rand Index
        
        Args:
            previous: Previous clustering state
            current: Current clustering state
            
        Returns:
            ClusteringEvent if significant change detected
        """
        try:
            # Calculate Adjusted Rand Index
            if len(previous.cluster_assignments) != len(current.cluster_assignments):
                logger.warning("Cluster assignment length mismatch")
                return None
            
            rand_index = adjusted_rand_score(
                previous.cluster_assignments, 
                current.cluster_assignments
            )
            
            # Detect different types of events
            event_type = "regime_change"
            significance_score = 1.0 - rand_index
            
            # Check for volatility spikes
            prev_vol_avg = np.mean(list(previous.volatility_profiles.values()))
            curr_vol_avg = np.mean(list(current.volatility_profiles.values()))
            vol_change = abs(curr_vol_avg - prev_vol_avg) / prev_vol_avg
            
            if vol_change > 0.5:  # 50% volatility change
                event_type = "volatility_spike"
                significance_score = max(significance_score, vol_change)
            
            # Check for correlation shifts
            corr_change = np.mean(np.abs(
                previous.correlation_matrix - current.correlation_matrix
            ))
            
            if corr_change > 0.3:  # 30% correlation change
                event_type = "correlation_shift"
                significance_score = max(significance_score, corr_change)
            
            # Determine affected pairs
            affected_pairs = []
            for i, (prev_cluster, curr_cluster) in enumerate(
                zip(previous.cluster_assignments, current.cluster_assignments)
            ):
                if prev_cluster != curr_cluster and i < len(current.symbols):
                    affected_pairs.append(current.symbols[i])
            
            # Create event if threshold exceeded
            if rand_index < self.event_threshold or significance_score > 0.3:
                return ClusteringEvent(
                    timestamp=current.timestamp,
                    event_type=event_type,
                    previous_state=previous,
                    current_state=current,
                    rand_index=rand_index,
                    significance_score=significance_score,
                    affected_pairs=affected_pairs,
                    description=self._generate_event_description(
                        event_type, rand_index, significance_score, affected_pairs
                    )
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error detecting regime change: {str(e)}", exc_info=True)
            return None
    
    def _calculate_regime_stability(self, cluster_assignments: List[int]) -> float:
        """
        Calculate regime stability score based on cluster distribution
        
        Args:
            cluster_assignments: Current cluster assignments
            
        Returns:
            Stability score (0-1, higher = more stable)
        """
        try:
            if not cluster_assignments:
                return 0.0
            
            # Calculate cluster size distribution
            unique_clusters, counts = np.unique(cluster_assignments, return_counts=True)
            cluster_sizes = counts / len(cluster_assignments)
            
            # Stability is higher when clusters are more evenly distributed
            # Use inverse of coefficient of variation
            if len(cluster_sizes) == 1:
                return 1.0
            
            cv = np.std(cluster_sizes) / np.mean(cluster_sizes)
            stability = 1.0 / (1.0 + cv)
            
            return min(max(stability, 0.0), 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating regime stability: {str(e)}")
            return 0.0
    
    def _generate_event_description(self, 
                                   event_type: str, 
                                   rand_index: float,
                                   significance_score: float,
                                   affected_pairs: List[str]) -> str:
        """Generate human-readable event description"""
        
        descriptions = {
            "regime_change": f"Market regime shift detected (ARI={rand_index:.3f})",
            "volatility_spike": f"Volatility spike detected (score={significance_score:.3f})",
            "correlation_shift": f"Correlation structure change (score={significance_score:.3f})"
        }
        
        base_desc = descriptions.get(event_type, "Market event detected")
        
        if affected_pairs:
            pair_list = ", ".join(affected_pairs[:5])
            if len(affected_pairs) > 5:
                pair_list += f" and {len(affected_pairs) - 5} others"
            base_desc += f". Affected pairs: {pair_list}"
        
        return base_desc

    def get_current_state(self) -> Optional[ClusteringState]:
        """Get current clustering state"""
        with self.lock:
            return self.current_state

    def get_recent_events(self, hours_back: int = 24) -> List[ClusteringEvent]:
        """
        Get recent events within specified time window

        Args:
            hours_back: Number of hours to look back

        Returns:
            List of recent events
        """
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)

        with self.lock:
            return [
                event for event in self.event_history
                if event.timestamp >= cutoff_time
            ]

    def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics"""
        with self.lock:
            uptime = (datetime.now(MARKET_TIMEZONE) - self.start_time).total_seconds() / 3600
            avg_processing_time = np.mean(self.processing_times) if self.processing_times else 0.0

            # Calculate average data quality from recent states
            recent_states = list(self.state_history)[-50:]  # Last 50 states
            avg_quality = np.mean([s.data_quality_score for s in recent_states]) if recent_states else 0.0

            return PerformanceMetrics(
                total_updates=self.update_count,
                events_detected=self.event_count,
                average_processing_time=avg_processing_time,
                data_quality_average=avg_quality,
                uptime_hours=uptime,
                last_update=self.current_state.timestamp if self.current_state else self.start_time
            )

    def get_state_history(self, limit: int = 100) -> List[ClusteringState]:
        """
        Get historical states

        Args:
            limit: Maximum number of states to return

        Returns:
            List of historical states (most recent first)
        """
        with self.lock:
            history = list(self.state_history)
            return history[-limit:] if limit else history

    def get_cluster_stability_trend(self, hours_back: int = 24) -> List[Tuple[datetime, float]]:
        """
        Get cluster stability trend over time

        Args:
            hours_back: Number of hours to analyze

        Returns:
            List of (timestamp, stability_score) tuples
        """
        cutoff_time = datetime.now(MARKET_TIMEZONE) - timedelta(hours=hours_back)

        with self.lock:
            trend_data = [
                (state.timestamp, state.regime_stability)
                for state in self.state_history
                if state.timestamp >= cutoff_time
            ]

            return sorted(trend_data, key=lambda x: x[0])

    def _persist_state(self):
        """Persist current state and recent history to disk and database"""
        try:
            # Database persistence (preferred)
            if self.use_database and self.db_manager:
                try:
                    # Store current state in database
                    if self.current_state:
                        self.db_manager.store_clustering_state(self.current_state)

                    # Store recent events in database
                    recent_events = list(self.event_history)[-10:]  # Last 10 events
                    for event in recent_events:
                        # For database storage, we need to handle the event structure
                        # Store basic event data without full state objects
                        self.db_manager.execute_query("""
                            INSERT OR IGNORE INTO clustering_events (
                                timestamp, event_type, rand_index, significance_score,
                                affected_pairs, description
                            ) VALUES (?, ?, ?, ?, ?, ?)
                        """, (
                            event.timestamp.isoformat(),
                            event.event_type,
                            event.rand_index,
                            event.significance_score,
                            json.dumps(event.affected_pairs),
                            event.description
                        ))

                    # Store performance metrics
                    metrics = self.get_performance_metrics()
                    self.db_manager.store_performance_metrics(metrics)

                    logger.debug("State persisted to database successfully")
                    return  # Skip file persistence if database works

                except Exception as e:
                    logger.error(f"Database persistence failed: {str(e)}")
                    # Fall back to file persistence

            # File persistence (fallback)
            # Save current state
            if self.current_state:
                state_file = self.persistence_dir / "current_state.pkl"
                with open(state_file, 'wb') as f:
                    pickle.dump(self.current_state, f)

            # Save recent events
            recent_events = list(self.event_history)[-100:]  # Last 100 events
            events_file = self.persistence_dir / "recent_events.pkl"
            with open(events_file, 'wb') as f:
                pickle.dump(recent_events, f)

            # Save performance metrics
            metrics = self.get_performance_metrics()
            metrics_file = self.persistence_dir / "performance_metrics.json"
            with open(metrics_file, 'w') as f:
                # Convert to dict and handle datetime serialization
                metrics_dict = asdict(metrics)
                metrics_dict['last_update'] = metrics_dict['last_update'].isoformat()
                json.dump(metrics_dict, f, indent=2)

            logger.debug("State persisted successfully")

        except Exception as e:
            logger.error(f"Error persisting state: {str(e)}", exc_info=True)

    def _load_persisted_state(self):
        """Load persisted state from disk"""
        try:
            # Load current state
            state_file = self.persistence_dir / "current_state.pkl"
            if state_file.exists():
                with open(state_file, 'rb') as f:
                    self.current_state = pickle.load(f)
                logger.info("Loaded persisted current state")

            # Load recent events
            events_file = self.persistence_dir / "recent_events.pkl"
            if events_file.exists():
                with open(events_file, 'rb') as f:
                    events = pickle.load(f)
                    self.event_history.extend(events)
                logger.info(f"Loaded {len(events)} persisted events")

            # Load performance metrics for continuity
            metrics_file = self.persistence_dir / "performance_metrics.json"
            if metrics_file.exists():
                with open(metrics_file, 'r') as f:
                    metrics_dict = json.load(f)
                    self.update_count = metrics_dict.get('total_updates', 0)
                    self.event_count = metrics_dict.get('events_detected', 0)
                logger.info("Loaded persisted performance metrics")

        except Exception as e:
            logger.error(f"Error loading persisted state: {str(e)}", exc_info=True)

    def clear_history(self):
        """Clear all historical data (keep current state)"""
        with self.lock:
            self.state_history.clear()
            self.event_history.clear()
            self.processing_times.clear()
            logger.info("State history cleared")

    def export_state_summary(self) -> Dict[str, Any]:
        """
        Export comprehensive state summary for external use

        Returns:
            Dictionary containing current state and statistics
        """
        with self.lock:
            summary = {
                'timestamp': datetime.now(MARKET_TIMEZONE).isoformat(),
                'current_state': None,
                'performance_metrics': asdict(self.get_performance_metrics()),
                'recent_events_count': len(self.get_recent_events(24)),
                'total_states_recorded': len(self.state_history),
                'system_status': 'active' if self.current_state else 'inactive'
            }

            # Add current state info if available
            if self.current_state:
                summary['current_state'] = {
                    'timestamp': self.current_state.timestamp.isoformat(),
                    'cluster_count': self.current_state.cluster_count,
                    'regime_stability': self.current_state.regime_stability,
                    'data_quality_score': self.current_state.data_quality_score,
                    'symbols_count': len(self.current_state.symbols)
                }

            # Convert datetime in performance metrics
            summary['performance_metrics']['last_update'] = summary['performance_metrics']['last_update'].isoformat()

            return summary
