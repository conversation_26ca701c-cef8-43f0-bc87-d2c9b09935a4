{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"gil-refs\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 1859062398649441551, "profile": 5688549466542000691, "path": 11984211450688993290, "deps": [[46745629712228035, "pyo3_ffi", false, 18272553533897193250], [557099714978251243, "build_script_build", false, 1614537162658709860], [629381703529241162, "indoc", false, 16401018344781987546], [2828590642173593838, "cfg_if", false, 17428403545301830978], [3722963349756955755, "once_cell", false, 10802249507119394238], [4684437522915235464, "libc", false, 10730761157540435168], [6110494908772664783, "pyo3_macros", false, 9699146869326228176], [14643204177830147187, "memoffset", false, 8052816519068297393], [14748792705540276325, "unindent", false, 13845445609147575258]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\pyo3-ea35780394eb47ab\\dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}