"""
Test script for the Dynamic FX Clustering Dashboard
"""

import sys
import os
import time
import threading
from datetime import datetime

# Add clustering directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'clustering'))

def test_dashboard_components():
    """Test dashboard components without running the server"""
    print("Testing Dashboard Components")
    print("=" * 50)
    
    try:
        # Test imports
        print("1. Testing imports...")
        import run_clustering_app
        from clustering.clustering_engine import ClusteringEngine
        from clustering.state_manager import StateManager
        from config import CURRENCY_PAIRS
        print("✓ All imports successful")
        
        # Test clustering engine initialization
        print("\n2. Testing ClusteringEngine initialization...")
        engine = ClusteringEngine(
            symbols=CURRENCY_PAIRS[:5],  # Use subset for testing
            event_threshold=0.7,
            min_data_quality=0.8,
            persistence_dir="data/test_clustering"
        )
        print(f"✓ ClusteringEngine created with {len(engine.symbols)} symbols")
        
        # Test dashboard state
        print("\n3. Testing dashboard state...")
        dashboard_state = run_clustering_app.dashboard_state
        print(f"✓ Dashboard state initialized: {dashboard_state}")
        
        # Test layout components
        print("\n4. Testing layout components...")
        header = run_clustering_app.create_header()
        control_panel = run_clustering_app.create_control_panel()
        main_panels = run_clustering_app.create_main_panels()
        print("✓ All layout components created successfully")
        
        # Test app initialization
        print("\n5. Testing Dash app initialization...")
        app = run_clustering_app.app
        print(f"✓ Dash app initialized: {app.title}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in dashboard test: {str(e)}")
        return False


def test_dashboard_data_flow():
    """Test data flow without MT5 connection"""
    print("\nTesting Dashboard Data Flow")
    print("=" * 50)
    
    try:
        from run_clustering_app import clustering_engine
        
        # Test engine status
        print("1. Testing engine status...")
        status = clustering_engine.get_current_status()
        print(f"✓ Engine status retrieved: {status['engine_status']}")
        print(f"  - Symbols: {status['symbols_count']}")
        print(f"  - Updates: {status['update_count']}")
        print(f"  - Errors: {status['error_count']}")
        
        # Test state manager
        print("\n2. Testing state manager...")
        current_state = clustering_engine.state_manager.get_current_state()
        if current_state:
            print(f"✓ Current state available: {current_state.cluster_count} clusters")
        else:
            print("✓ No current state (expected for new installation)")
        
        # Test recent events
        print("\n3. Testing event retrieval...")
        recent_events = clustering_engine.state_manager.get_recent_events(hours_back=24)
        print(f"✓ Recent events retrieved: {len(recent_events)} events")
        
        # Test performance metrics
        print("\n4. Testing performance metrics...")
        metrics = clustering_engine.state_manager.get_performance_metrics()
        print(f"✓ Performance metrics retrieved:")
        print(f"  - Total updates: {metrics.total_updates}")
        print(f"  - Events detected: {metrics.events_detected}")
        print(f"  - Uptime: {metrics.uptime_hours:.1f}h")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in data flow test: {str(e)}")
        return False


def test_dashboard_mock_data():
    """Test dashboard with mock data"""
    print("\nTesting Dashboard with Mock Data")
    print("=" * 50)
    
    try:
        import numpy as np
        from clustering.state_manager import ClusteringState
        from config import CURRENCY_PAIRS
        
        # Create mock clustering state
        print("1. Creating mock clustering state...")
        mock_correlation_matrix = np.random.rand(5, 5)
        mock_correlation_matrix = (mock_correlation_matrix + mock_correlation_matrix.T) / 2
        np.fill_diagonal(mock_correlation_matrix, 1.0)
        
        mock_state = ClusteringState(
            timestamp=datetime.now(),
            correlation_matrix=mock_correlation_matrix,
            cluster_assignments=[0, 0, 1, 1, 2],
            cluster_count=3,
            symbols=CURRENCY_PAIRS[:5],
            volatility_profiles={pair: np.random.rand() for pair in CURRENCY_PAIRS[:5]},
            regime_stability=0.85,
            data_quality_score=0.92
        )
        print("✓ Mock clustering state created")
        
        # Test data formatting for dashboard
        print("\n2. Testing data formatting...")
        clustering_data = {
            'current_state': {
                'timestamp': mock_state.timestamp.isoformat(),
                'cluster_count': mock_state.cluster_count,
                'cluster_assignments': mock_state.cluster_assignments,
                'symbols': mock_state.symbols,
                'regime_stability': mock_state.regime_stability,
                'data_quality_score': mock_state.data_quality_score,
                'correlation_matrix': mock_state.correlation_matrix.tolist()
            },
            'recent_events': [],
            'last_update': datetime.now().strftime('%H:%M:%S')
        }
        print("✓ Data formatted for dashboard")
        print(f"  - Clusters: {clustering_data['current_state']['cluster_count']}")
        print(f"  - Symbols: {len(clustering_data['current_state']['symbols'])}")
        print(f"  - Quality: {clustering_data['current_state']['data_quality_score']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"✗ Error in mock data test: {str(e)}")
        return False


if __name__ == "__main__":
    print("Dynamic FX Clustering Dashboard Test Suite")
    print("=" * 60)
    
    # Run tests
    test1 = test_dashboard_components()
    test2 = test_dashboard_data_flow()
    test3 = test_dashboard_mock_data()
    
    print("\n" + "=" * 60)
    print("TEST RESULTS:")
    print(f"✓ Component Test: {'PASSED' if test1 else 'FAILED'}")
    print(f"✓ Data Flow Test: {'PASSED' if test2 else 'FAILED'}")
    print(f"✓ Mock Data Test: {'PASSED' if test3 else 'FAILED'}")
    
    if all([test1, test2, test3]):
        print("\n🎉 All dashboard tests PASSED!")
        print("\nTo start the dashboard:")
        print("python run_clustering_app.py")
        print("\nThen open: http://127.0.0.1:8050")
    else:
        print("\n❌ Some tests FAILED - check errors above")
