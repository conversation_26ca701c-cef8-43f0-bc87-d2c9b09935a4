{"rustc": 1842507548689473721, "features": "[\"default\"]", "declared_features": "[\"benchmarks\", \"blas\", \"criterion\", \"default\", \"intel-mkl-static\", \"intel-mkl-system\", \"ndarray-linalg\", \"netlib-static\", \"netlib-system\", \"openblas-static\", \"openblas-system\", \"pprof\", \"serde\", \"serde_crate\"]", "target": 11587406923222373526, "profile": 2040997289075261528, "path": 5004194065723628634, "deps": [[2289341005599476083, "approx", false, 14167278476402842237], [3008854931152362171, "n<PERSON><PERSON>", false, 16944662187584126042], [3208259674829469253, "build_script_build", false, 5794654779902562045], [5157631553186200874, "num_traits", false, 8484145598454306592], [8008191657135824715, "thiserror", false, 7005268391758426939], [11422141427705954375, "sprs", false, 8463337202550467960], [13208667028893622512, "rand", false, 1999665565350161933]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\linfa-45af701376823e05\\dep-lib-linfa", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}