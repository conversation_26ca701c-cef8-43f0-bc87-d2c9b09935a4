{"rustc": 1842507548689473721, "features": "[\"default\", \"extension-module\", \"indoc\", \"macros\", \"pyo3-macros\", \"unindent\"]", "declared_features": "[\"abi3\", \"abi3-py310\", \"abi3-py311\", \"abi3-py312\", \"abi3-py37\", \"abi3-py38\", \"abi3-py39\", \"anyhow\", \"auto-initialize\", \"chrono\", \"chrono-tz\", \"default\", \"either\", \"experimental-async\", \"experimental-inspect\", \"extension-module\", \"eyre\", \"full\", \"generate-import-lib\", \"gil-refs\", \"hashbrown\", \"indexmap\", \"indoc\", \"inventory\", \"macros\", \"multiple-pymethods\", \"nightly\", \"num-bigint\", \"num-complex\", \"num-rational\", \"py-clone\", \"pyo3-macros\", \"rust_decimal\", \"serde\", \"smallvec\", \"unindent\"]", "target": 1859062398649441551, "profile": 7086611809595859799, "path": 11984211450688993290, "deps": [[46745629712228035, "pyo3_ffi", false, 7576412310756427918], [557099714978251243, "build_script_build", false, 17230744041396984049], [629381703529241162, "indoc", false, 9107386474321796607], [2828590642173593838, "cfg_if", false, 10118896523543894662], [3722963349756955755, "once_cell", false, 13244492203321078007], [4684437522915235464, "libc", false, 9685844023081248841], [6110494908772664783, "pyo3_macros", false, 531725524297568813], [14643204177830147187, "memoffset", false, 995327542446860875], [14748792705540276325, "unindent", false, 17135912380718091844]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-6fd62655c06f5996\\dep-lib-pyo3", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}