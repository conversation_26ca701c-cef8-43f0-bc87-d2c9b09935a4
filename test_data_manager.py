"""
Test script for ClusteringDataManager
Verifies MT5 connection, data fetching, and weekend support
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# Add clustering directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'clustering'))

from clustering.data_manager import ClusteringDataManager
from config import CURRENCY_PAIRS, MARKET_TIMEZONE
from weekend_utils import is_weekend, get_weekend_status_message

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_data_manager():
    """Test the ClusteringDataManager functionality"""
    
    print("=" * 60)
    print("TESTING CLUSTERING DATA MANAGER")
    print("=" * 60)
    
    # Initialize data manager
    print("\n1. Initializing ClusteringDataManager...")
    data_manager = ClusteringDataManager()
    
    # Test connection
    print("\n2. Testing MT5 connection...")
    if data_manager.connect():
        print("✓ MT5 connection successful")
    else:
        print("✗ MT5 connection failed")
        return False
    
    # Check weekend status
    print(f"\n3. Weekend status: {get_weekend_status_message()}")
    
    # Test data fetching with a small subset of pairs
    test_pairs = CURRENCY_PAIRS[:5]  # Test with first 5 pairs
    print(f"\n4. Testing data fetch for {len(test_pairs)} pairs: {test_pairs}")
    
    try:
        # Fetch 24 hours of data
        data = data_manager.fetch_clustering_data(
            pairs=test_pairs,
            hours_back=24,
            use_weekend_fallback=True
        )
        
        if data:
            print(f"✓ Successfully fetched data for {len(data)} pairs")
            
            # Show data summary
            summary = data_manager.get_data_summary(data)
            print(f"\nData Summary:")
            print(f"  - Pairs: {summary.get('pairs_count', 0)}")
            print(f"  - Total records: {summary.get('total_records', 0)}")
            
            if 'time_range' in summary and summary['time_range']:
                time_range = summary['time_range']
                print(f"  - Time range: {time_range.get('start')} to {time_range.get('end')}")
                print(f"  - Duration: {time_range.get('duration_hours', 0):.1f} hours")
            
            # Test data preparation for Rust
            print(f"\n5. Testing data preparation for Rust...")
            price_matrix = data_manager.prepare_data_for_rust(data)
            
            if price_matrix:
                print(f"✓ Prepared {len(price_matrix)} x {len(price_matrix[0]) if price_matrix else 0} price matrix")
                
                # Show sample data
                if len(price_matrix) >= 5:
                    print(f"\nSample data (first 5 rows):")
                    for i, row in enumerate(price_matrix[:5]):
                        formatted_row = [f"{val:.5f}" for val in row[:3]]  # Show first 3 values
                        print(f"  Row {i}: [{', '.join(formatted_row)}...]")
            else:
                print("✗ Failed to prepare data for Rust")
                
        else:
            print("✗ No data fetched")
            
    except Exception as e:
        print(f"✗ Error during data fetch: {str(e)}")
        logger.error(f"Data fetch error: {str(e)}", exc_info=True)
    
    # Test individual pair data
    print(f"\n6. Testing individual pair data fetch...")
    try:
        test_pair = test_pairs[0]
        end_time = datetime.now(MARKET_TIMEZONE)
        start_time = end_time - timedelta(hours=2)
        
        pair_data = data_manager._fetch_pair_data(test_pair, start_time, end_time)
        
        if pair_data is not None and not pair_data.empty:
            print(f"✓ Successfully fetched {len(pair_data)} records for {test_pair}")
            print(f"  Columns: {list(pair_data.columns)}")
            print(f"  Time range: {pair_data.index.min()} to {pair_data.index.max()}")
            
            # Show sample data
            if len(pair_data) >= 3:
                print(f"\nSample {test_pair} data:")
                sample_data = pair_data.head(3)[['open', 'high', 'low', 'close']]
                for idx, row in sample_data.iterrows():
                    print(f"  {idx}: O={row['open']:.5f} H={row['high']:.5f} L={row['low']:.5f} C={row['close']:.5f}")
        else:
            print(f"✗ No data fetched for {test_pair}")
            
    except Exception as e:
        print(f"✗ Error fetching individual pair data: {str(e)}")
        logger.error(f"Individual pair fetch error: {str(e)}", exc_info=True)
    
    # Cleanup
    print(f"\n7. Cleaning up...")
    data_manager.disconnect()
    print("✓ Disconnected from MT5")
    
    print("\n" + "=" * 60)
    print("DATA MANAGER TEST COMPLETED")
    print("=" * 60)
    
    return True


def test_weekend_functionality():
    """Test weekend-specific functionality"""
    
    print("\n" + "=" * 60)
    print("TESTING WEEKEND FUNCTIONALITY")
    print("=" * 60)
    
    from weekend_utils import (
        is_weekend, get_last_friday_end, get_weekend_time_range,
        should_use_friday_data, cache_friday_data, get_cached_friday_data
    )
    
    print(f"\n1. Weekend status check:")
    print(f"  - Is weekend: {is_weekend()}")
    print(f"  - Should use Friday data: {should_use_friday_data()}")
    
    print(f"\n2. Friday time calculations:")
    friday_end = get_last_friday_end()
    print(f"  - Last Friday end: {friday_end}")
    
    weekend_start, weekend_end = get_weekend_time_range()
    print(f"  - Weekend time range: {weekend_start} to {weekend_end}")
    
    print(f"\n3. Testing data caching:")
    test_data = {"test": "weekend_data", "timestamp": datetime.now()}
    cache_friday_data("test_key", test_data)
    print("  ✓ Data cached")
    
    cached_data = get_cached_friday_data("test_key")
    if cached_data:
        print("  ✓ Data retrieved from cache")
    else:
        print("  - No cached data (expected during weekdays)")
    
    print("\n" + "=" * 60)
    print("WEEKEND FUNCTIONALITY TEST COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    print("Starting ClusteringDataManager tests...")
    
    try:
        # Test basic data manager functionality
        success = test_data_manager()
        
        # Test weekend functionality
        test_weekend_functionality()
        
        if success:
            print("\n🎉 All tests completed successfully!")
        else:
            print("\n❌ Some tests failed. Check the logs above.")
            
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        logger.error(f"Test execution error: {str(e)}", exc_info=True)
    
    print("\nTest script finished.")
