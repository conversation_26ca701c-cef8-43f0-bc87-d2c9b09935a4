"""
Test script for ClusteringEngine
Verifies complete integration of data manager, clustering, and state management
"""

import sys
import os
import logging
from datetime import datetime
import time

# Add clustering directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'clustering'))

from clustering.clustering_engine import ClusteringEngine
from config import CURRENCY_PAIRS, MARKET_TIMEZONE

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_clustering_engine_initialization():
    """Test ClusteringEngine initialization"""
    
    print("=" * 60)
    print("TESTING CLUSTERING ENGINE - INITIALIZATION")
    print("=" * 60)
    
    print("\n1. Testing default initialization...")
    engine = ClusteringEngine()
    print(f"✓ Engine initialized with {len(engine.symbols)} symbols")
    
    print("\n2. Testing custom initialization...")
    test_symbols = CURRENCY_PAIRS[:10]  # Use subset for testing
    custom_engine = ClusteringEngine(
        symbols=test_symbols,
        event_threshold=0.6,
        min_data_quality=0.7,
        persistence_dir="test_data/clustering_engine"
    )
    print(f"✓ Custom engine initialized with {len(custom_engine.symbols)} symbols")
    
    return custom_engine


def test_clustering_engine_connection(engine):
    """Test ClusteringEngine connection"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - CONNECTION")
    print("=" * 60)
    
    print("\n1. Testing connection...")
    if engine.connect():
        print("✓ Successfully connected to data sources")
        return True
    else:
        print("✗ Failed to connect to data sources")
        return False


def test_clustering_analysis(engine):
    """Test complete clustering analysis pipeline"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - ANALYSIS PIPELINE")
    print("=" * 60)
    
    print("\n1. Running clustering analysis...")
    
    try:
        # Run analysis with shorter time window for testing
        event = engine.run_clustering_analysis(
            hours_back=2,  # Shorter window for faster testing
            use_weekend_fallback=True
        )
        
        print("✓ Clustering analysis completed")
        
        if event:
            print(f"  🚨 Event detected: {event.event_type}")
            print(f"    - ARI: {event.rand_index:.3f}")
            print(f"    - Significance: {event.significance_score:.3f}")
            print(f"    - Affected pairs: {len(event.affected_pairs)}")
            print(f"    - Description: {event.description}")
        else:
            print("  - No events detected")
        
        return event
        
    except Exception as e:
        print(f"✗ Clustering analysis failed: {str(e)}")
        logger.error(f"Clustering analysis error: {str(e)}", exc_info=True)
        return None


def test_engine_status(engine):
    """Test engine status and metrics"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - STATUS & METRICS")
    print("=" * 60)
    
    print("\n1. Getting engine status...")
    status = engine.get_current_status()
    
    print(f"✓ Engine status retrieved:")
    print(f"  - Engine status: {status.get('engine_status', 'unknown')}")
    print(f"  - Symbols count: {status.get('symbols_count', 0)}")
    print(f"  - Update count: {status.get('update_count', 0)}")
    print(f"  - Error count: {status.get('error_count', 0)}")
    print(f"  - Error rate: {status.get('error_rate', 0):.3f}")
    print(f"  - Current clusters: {status.get('current_clusters', 0)}")
    print(f"  - Regime stability: {status.get('regime_stability', 0):.3f}")
    print(f"  - Data quality: {status.get('data_quality', 0):.3f}")
    print(f"  - Recent events (24h): {status.get('recent_events_24h', 0)}")
    
    if 'performance_metrics' in status:
        perf = status['performance_metrics']
        print(f"  - Performance metrics:")
        print(f"    * Total updates: {perf.get('total_updates', 0)}")
        print(f"    * Events detected: {perf.get('events_detected', 0)}")
        print(f"    * Avg processing time: {perf.get('average_processing_time', 0):.4f}s")
        print(f"    * Uptime: {perf.get('uptime_hours', 0):.2f}h")
    
    return status


def test_multiple_analysis_runs(engine):
    """Test multiple analysis runs to simulate real-time operation"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - MULTIPLE RUNS")
    print("=" * 60)
    
    print("\n1. Running multiple analysis cycles...")
    
    events_detected = []
    run_count = 3
    
    for i in range(run_count):
        print(f"\n  Run {i+1}/{run_count}:")
        
        try:
            event = engine.run_clustering_analysis(
                hours_back=1,  # Very short window for testing
                use_weekend_fallback=True
            )
            
            if event:
                events_detected.append(event)
                print(f"    ✓ Event detected: {event.event_type}")
            else:
                print(f"    - No event detected")
            
            # Small delay between runs
            time.sleep(0.5)
            
        except Exception as e:
            print(f"    ✗ Run {i+1} failed: {str(e)}")
    
    print(f"\n2. Multiple runs summary:")
    print(f"  - Total runs: {run_count}")
    print(f"  - Events detected: {len(events_detected)}")
    print(f"  - Success rate: {(run_count - engine.error_count) / run_count:.1%}")
    
    return events_detected


def test_state_export(engine):
    """Test state export functionality"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - STATE EXPORT")
    print("=" * 60)
    
    print("\n1. Exporting complete state summary...")
    
    try:
        summary = engine.export_state_summary()
        
        print("✓ State summary exported successfully")
        print(f"  - Export timestamp: {summary.get('export_timestamp', 'unknown')}")
        
        if 'engine' in summary:
            engine_info = summary['engine']
            print(f"  - Engine status: {engine_info.get('engine_status', 'unknown')}")
            print(f"  - Total updates: {engine_info.get('update_count', 0)}")
        
        if 'state_manager' in summary:
            state_info = summary['state_manager']
            print(f"  - System status: {state_info.get('system_status', 'unknown')}")
            print(f"  - States recorded: {state_info.get('total_states_recorded', 0)}")
            print(f"  - Recent events: {state_info.get('recent_events_count', 0)}")
        
        return summary
        
    except Exception as e:
        print(f"✗ State export failed: {str(e)}")
        logger.error(f"State export error: {str(e)}", exc_info=True)
        return None


def test_recent_events(engine):
    """Test recent events retrieval"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - RECENT EVENTS")
    print("=" * 60)
    
    print("\n1. Getting recent events...")
    
    try:
        events_24h = engine.get_recent_events(hours_back=24)
        events_1h = engine.get_recent_events(hours_back=1)
        
        print(f"✓ Recent events retrieved:")
        print(f"  - Events in last 24h: {len(events_24h)}")
        print(f"  - Events in last 1h: {len(events_1h)}")
        
        if events_24h:
            print(f"\n  Recent events summary:")
            for i, event in enumerate(events_24h[-3:]):  # Show last 3 events
                print(f"    {i+1}. {event.event_type} at {event.timestamp}")
                print(f"       ARI: {event.rand_index:.3f}, Affected: {len(event.affected_pairs)} pairs")
        
        return events_24h
        
    except Exception as e:
        print(f"✗ Recent events retrieval failed: {str(e)}")
        logger.error(f"Recent events error: {str(e)}", exc_info=True)
        return []


def test_error_handling(engine):
    """Test error handling and recovery"""
    
    print("\n" + "=" * 60)
    print("TESTING CLUSTERING ENGINE - ERROR HANDLING")
    print("=" * 60)
    
    print("\n1. Testing with invalid parameters...")
    
    # Test with very short time window (should handle gracefully)
    try:
        event = engine.run_clustering_analysis(
            hours_back=0.01,  # Very short window
            use_weekend_fallback=True
        )
        
        if event is None:
            print("✓ Gracefully handled insufficient data")
        else:
            print("✓ Analysis succeeded despite short window")
            
    except Exception as e:
        print(f"- Expected error handled: {str(e)}")
    
    # Test status retrieval after potential errors
    print("\n2. Testing status after error conditions...")
    status = engine.get_current_status()
    
    if status:
        print(f"✓ Status retrieval works after errors")
        print(f"  - Error count: {status.get('error_count', 0)}")
        print(f"  - Error rate: {status.get('error_rate', 0):.3f}")
    else:
        print("✗ Status retrieval failed")


if __name__ == "__main__":
    print("Starting ClusteringEngine integration tests...")
    
    try:
        # Test initialization
        engine = test_clustering_engine_initialization()
        
        # Test connection
        if test_clustering_engine_connection(engine):
            
            # Test clustering analysis
            event = test_clustering_analysis(engine)
            
            # Test engine status
            status = test_engine_status(engine)
            
            # Test multiple runs
            events = test_multiple_analysis_runs(engine)
            
            # Test state export
            summary = test_state_export(engine)
            
            # Test recent events
            recent_events = test_recent_events(engine)
            
            # Test error handling
            test_error_handling(engine)
            
            # Cleanup
            engine.disconnect()
            
            print("\n" + "=" * 60)
            print("ALL CLUSTERING ENGINE TESTS COMPLETED")
            print("=" * 60)
            print(f"🎉 ClusteringEngine integration is working correctly!")
            print(f"📊 Summary:")
            print(f"  - Analysis runs completed: {engine.update_count}")
            print(f"  - Events detected: {len(recent_events)}")
            print(f"  - Error rate: {engine.error_count / max(engine.update_count, 1):.1%}")
            
        else:
            print("\n❌ Connection failed - skipping integration tests")
            print("Make sure MT5 is running and accessible")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        logger.error(f"Test execution error: {str(e)}", exc_info=True)
    
    print("\nClusteringEngine integration test finished.")
