"""
Test script for DatabaseManager
Verifies database functionality for clustering application
"""

import sys
import os
import logging
from datetime import datetime, timedelta
import numpy as np
from pathlib import Path
import tempfile
import shutil

# Add clustering directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'clustering'))

from clustering.database import DatabaseManager
from clustering.state_manager import ClusteringState, ClusteringEvent, PerformanceMetrics
from config import MARKET_TIMEZONE

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_test_clustering_state(timestamp: datetime = None) -> ClusteringState:
    """Create test clustering state"""
    if not timestamp:
        timestamp = datetime.now(MARKET_TIMEZONE)
    
    # Create test correlation matrix
    n_symbols = 8
    correlation_matrix = np.random.rand(n_symbols, n_symbols)
    correlation_matrix = (correlation_matrix + correlation_matrix.T) / 2  # Make symmetric
    np.fill_diagonal(correlation_matrix, 1.0)  # Diagonal should be 1
    
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'USDCAD', 'AUDUSD', 'NZDUSD', 'EURJPY']
    cluster_assignments = [0, 0, 1, 1, 1, 2, 2, 0]  # 3 clusters
    volatility_profiles = {symbol: np.random.uniform(0.01, 0.05) for symbol in symbols}
    
    return ClusteringState(
        timestamp=timestamp,
        correlation_matrix=correlation_matrix,
        cluster_assignments=cluster_assignments,
        cluster_count=3,
        symbols=symbols,
        volatility_profiles=volatility_profiles,
        regime_stability=np.random.uniform(0.5, 1.0),
        data_quality_score=np.random.uniform(0.8, 1.0)
    )


def create_simple_event_data(timestamp: datetime = None) -> dict:
    """Create simple event data for database testing"""
    if not timestamp:
        timestamp = datetime.now(MARKET_TIMEZONE)

    return {
        'timestamp': timestamp,
        'event_type': "regime_change",
        'rand_index': np.random.uniform(0.0, 0.7),
        'significance_score': np.random.uniform(1.0, 3.0),
        'affected_pairs': ['EURUSD', 'GBPUSD', 'USDJPY'],
        'description': "Test regime change event"
    }


def create_test_performance_metrics(timestamp: datetime = None) -> PerformanceMetrics:
    """Create test performance metrics"""
    if not timestamp:
        timestamp = datetime.now(MARKET_TIMEZONE)
    
    return PerformanceMetrics(
        total_updates=100,
        events_detected=5,
        average_processing_time=0.15,
        data_quality_average=0.92,
        uptime_hours=24.5,
        last_update=timestamp
    )


def test_database_initialization():
    """Test database initialization and connection"""
    
    print("=" * 60)
    print("TESTING DATABASE - INITIALIZATION")
    print("=" * 60)
    
    # Create temporary database for testing
    temp_dir = tempfile.mkdtemp()
    test_db_path = os.path.join(temp_dir, "test_clustering.db")
    
    print(f"\n1. Creating test database: {test_db_path}")
    
    try:
        db_manager = DatabaseManager(db_path=test_db_path)
        print("✓ DatabaseManager created")
        
        print("\n2. Testing database connection...")
        if db_manager.connect():
            print("✓ Database connection successful")
            print("✓ Schema initialized")
            
            return db_manager, temp_dir
        else:
            print("✗ Database connection failed")
            return None, temp_dir
            
    except Exception as e:
        print(f"✗ Database initialization failed: {str(e)}")
        return None, temp_dir


def test_clustering_state_storage(db_manager: DatabaseManager):
    """Test clustering state storage and retrieval"""
    
    print("\n" + "=" * 60)
    print("TESTING DATABASE - CLUSTERING STATES")
    print("=" * 60)
    
    print("\n1. Creating test clustering states...")
    
    # Create multiple test states with different timestamps
    states = []
    base_time = datetime.now(MARKET_TIMEZONE)
    
    for i in range(5):
        timestamp = base_time - timedelta(hours=i)
        state = create_test_clustering_state(timestamp)
        states.append(state)
    
    print(f"✓ Created {len(states)} test states")
    
    print("\n2. Storing states in database...")
    stored_ids = []
    
    for i, state in enumerate(states):
        state_id = db_manager.store_clustering_state(state)
        if state_id:
            stored_ids.append(state_id)
            print(f"  ✓ State {i+1} stored with ID: {state_id}")
        else:
            print(f"  ✗ Failed to store state {i+1}")
    
    print(f"\n3. Retrieving states from database...")
    retrieved_states = db_manager.get_clustering_states(limit=10)
    
    print(f"✓ Retrieved {len(retrieved_states)} states")
    
    if retrieved_states:
        latest_state = retrieved_states[0]
        print(f"  - Latest state: {latest_state.cluster_count} clusters")
        print(f"  - Regime stability: {latest_state.regime_stability:.3f}")
        print(f"  - Data quality: {latest_state.data_quality_score:.3f}")
        print(f"  - Symbols: {len(latest_state.symbols)}")
    
    return stored_ids


def test_clustering_event_storage(db_manager: DatabaseManager):
    """Test clustering event storage and retrieval using direct database operations"""

    print("\n" + "=" * 60)
    print("TESTING DATABASE - CLUSTERING EVENTS")
    print("=" * 60)

    print("\n1. Creating test event data...")

    # Create multiple test events using direct database insertion
    base_time = datetime.now(MARKET_TIMEZONE)
    event_types = ["regime_change", "volatility_spike", "correlation_shift"]

    stored_event_ids = []

    print("\n2. Storing events in database using direct SQL...")

    for i in range(6):
        timestamp = base_time - timedelta(minutes=i*30)
        event_type = event_types[i % len(event_types)]

        try:
            cursor = db_manager.connection.cursor()
            cursor.execute("""
                INSERT INTO clustering_events (
                    timestamp, event_type, rand_index, significance_score,
                    affected_pairs, description
                ) VALUES (?, ?, ?, ?, ?, ?)
            """, (
                timestamp.isoformat(),
                event_type,
                np.random.uniform(0.0, 0.7),
                np.random.uniform(1.0, 3.0),
                '["EURUSD", "GBPUSD", "USDJPY"]',
                f"Test {event_type} event"
            ))

            event_id = cursor.lastrowid
            db_manager.connection.commit()
            stored_event_ids.append(event_id)
            print(f"  ✓ Event {i+1} ({event_type}) stored with ID: {event_id}")

        except Exception as e:
            print(f"  ✗ Failed to store event {i+1}: {str(e)}")

    print(f"\n3. Retrieving events using direct SQL...")

    try:
        cursor = db_manager.connection.cursor()
        cursor.execute("SELECT * FROM clustering_events ORDER BY timestamp DESC LIMIT 10")
        rows = cursor.fetchall()

        print(f"✓ Retrieved {len(rows)} events")

        if rows:
            print(f"  Recent events:")
            for i, row in enumerate(rows[:3]):
                print(f"    {i+1}. {row[2]} (ARI: {row[3]:.3f})")

        # Test event type filtering
        cursor.execute("SELECT COUNT(*) FROM clustering_events WHERE event_type = ?", ("regime_change",))
        regime_count = cursor.fetchone()[0]
        print(f"\n4. Event type filtering test:")
        print(f"✓ Found {regime_count} regime_change events")

    except Exception as e:
        print(f"✗ Event retrieval failed: {str(e)}")

    return stored_event_ids


def test_performance_metrics_storage(db_manager: DatabaseManager):
    """Test performance metrics storage and retrieval"""
    
    print("\n" + "=" * 60)
    print("TESTING DATABASE - PERFORMANCE METRICS")
    print("=" * 60)
    
    print("\n1. Creating test performance metrics...")
    
    # Create multiple metrics records
    metrics_list = []
    base_time = datetime.now(MARKET_TIMEZONE)
    
    for i in range(4):
        timestamp = base_time - timedelta(hours=i*6)
        metrics = create_test_performance_metrics(timestamp)
        metrics.total_updates = 50 + i * 25
        metrics.events_detected = i * 2
        metrics_list.append(metrics)
    
    print(f"✓ Created {len(metrics_list)} test metrics records")
    
    print("\n2. Storing metrics in database...")
    stored_metrics_ids = []
    
    for i, metrics in enumerate(metrics_list):
        metrics_id = db_manager.store_performance_metrics(metrics)
        if metrics_id:
            stored_metrics_ids.append(metrics_id)
            print(f"  ✓ Metrics {i+1} stored with ID: {metrics_id}")
        else:
            print(f"  ✗ Failed to store metrics {i+1}")
    
    print(f"\n3. Retrieving metrics from database...")
    retrieved_metrics = db_manager.get_performance_metrics_history(limit=10)
    
    print(f"✓ Retrieved {len(retrieved_metrics)} metrics records")
    
    if retrieved_metrics:
        latest_metrics = retrieved_metrics[0]
        print(f"  - Latest metrics:")
        print(f"    * Total updates: {latest_metrics['total_updates']}")
        print(f"    * Events detected: {latest_metrics['events_detected']}")
        print(f"    * Avg processing time: {latest_metrics['average_processing_time']:.4f}s")
        print(f"    * Data quality avg: {latest_metrics['data_quality_average']:.3f}")
    
    return stored_metrics_ids


def test_database_statistics(db_manager: DatabaseManager):
    """Test database statistics and utility functions"""
    
    print("\n" + "=" * 60)
    print("TESTING DATABASE - STATISTICS & UTILITIES")
    print("=" * 60)
    
    print("\n1. Getting database statistics...")
    stats = db_manager.get_database_statistics()
    
    if stats:
        print("✓ Database statistics retrieved:")
        print(f"  - Database size: {stats['database_size_mb']} MB")
        print(f"  - Total states: {stats['total_states']}")
        print(f"  - Total events: {stats['total_events']}")
        print(f"  - Total metrics: {stats['total_metrics']}")
        print(f"  - Recent activity (24h): {stats['recent_activity_24h']['events']} events, {stats['recent_activity_24h']['states']} states")
        print(f"  - Schema version: {stats['schema_version']}")
    else:
        print("✗ Failed to retrieve database statistics")
    
    print("\n2. Testing custom query execution...")
    try:
        # Test custom query
        results = db_manager.execute_query("SELECT COUNT(*) FROM clustering_events WHERE event_type = ?", ("regime_change",))
        if results:
            regime_count = results[0][0]
            print(f"✓ Custom query executed: {regime_count} regime_change events found")
        else:
            print("✓ Custom query executed (no results)")
    except Exception as e:
        print(f"✗ Custom query failed: {str(e)}")
    
    return stats


def test_database_backup_restore(db_manager: DatabaseManager, temp_dir: str):
    """Test database backup and restore functionality"""
    
    print("\n" + "=" * 60)
    print("TESTING DATABASE - BACKUP & RESTORE")
    print("=" * 60)
    
    backup_path = os.path.join(temp_dir, "test_backup.db")
    
    print(f"\n1. Creating database backup...")
    if db_manager.backup_database(backup_path):
        print(f"✓ Backup created: {backup_path}")
        
        # Verify backup file exists
        if Path(backup_path).exists():
            backup_size = Path(backup_path).stat().st_size
            print(f"  - Backup file size: {backup_size} bytes")
        else:
            print("✗ Backup file not found")
            return False
    else:
        print("✗ Backup creation failed")
        return False
    
    print(f"\n2. Testing backup restore...")
    if db_manager.restore_database(backup_path):
        print("✓ Database restored from backup")
        
        # Verify data integrity after restore
        stats = db_manager.get_database_statistics()
        if stats:
            print(f"  - Verified: {stats['total_states']} states, {stats['total_events']} events")
        
        return True
    else:
        print("✗ Database restore failed")
        return False


def cleanup_test_database(temp_dir: str):
    """Clean up test database and temporary files"""
    try:
        shutil.rmtree(temp_dir)
        print(f"\n✓ Test database cleaned up: {temp_dir}")
    except Exception as e:
        print(f"\n✗ Cleanup failed: {str(e)}")


if __name__ == "__main__":
    print("Starting DatabaseManager tests...")
    
    db_manager = None
    temp_dir = None
    
    try:
        # Test initialization
        db_manager, temp_dir = test_database_initialization()
        
        if db_manager:
            # Test clustering state storage
            state_ids = test_clustering_state_storage(db_manager)
            
            # Test clustering event storage
            event_ids = test_clustering_event_storage(db_manager)
            
            # Test performance metrics storage
            metrics_ids = test_performance_metrics_storage(db_manager)
            
            # Test database statistics
            stats = test_database_statistics(db_manager)
            
            # Test backup and restore
            backup_success = test_database_backup_restore(db_manager, temp_dir)
            
            # Disconnect
            db_manager.disconnect()
            
            print("\n" + "=" * 60)
            print("ALL DATABASE TESTS COMPLETED")
            print("=" * 60)
            print("🎉 DatabaseManager is working correctly!")
            print(f"📊 Summary:")
            print(f"  - States stored: {len(state_ids) if state_ids else 0}")
            print(f"  - Events stored: {len(event_ids) if event_ids else 0}")
            print(f"  - Metrics stored: {len(metrics_ids) if metrics_ids else 0}")
            print(f"  - Backup/restore: {'✓' if backup_success else '✗'}")
            
        else:
            print("\n❌ Database initialization failed - skipping tests")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        logger.error(f"Test execution error: {str(e)}", exc_info=True)
    
    finally:
        # Cleanup
        if temp_dir:
            cleanup_test_database(temp_dir)
    
    print("\nDatabaseManager test finished.")
