cargo:rustc-check-cfg=cfg(Py_LIMITED_API)
cargo:rustc-check-cfg=cfg(PyPy)
cargo:rustc-check-cfg=cfg(GraalPy)
cargo:rustc-check-cfg=cfg(py_sys_config, values("Py_DEBUG", "Py_REF_DEBUG", "Py_TRACE_REFS", "COUNT_ALLOCS"))
cargo:rustc-check-cfg=cfg(invalid_from_utf8_lint)
cargo:rustc-check-cfg=cfg(pyo3_disable_reference_pool)
cargo:rustc-check-cfg=cfg(pyo3_leak_on_drop_without_reference_pool)
cargo:rustc-check-cfg=cfg(diagnostic_namespace)
cargo:rustc-check-cfg=cfg(c_str_lit)
cargo:rustc-check-cfg=cfg(Py_3_7)
cargo:rustc-check-cfg=cfg(Py_3_8)
cargo:rustc-check-cfg=cfg(Py_3_9)
cargo:rustc-check-cfg=cfg(Py_3_10)
cargo:rustc-check-cfg=cfg(Py_3_11)
cargo:rustc-check-cfg=cfg(Py_3_12)
cargo:rustc-check-cfg=cfg(Py_3_13)
cargo:rerun-if-env-changed=PYO3_CROSS
cargo:rerun-if-env-changed=PYO3_CROSS_LIB_DIR
cargo:rerun-if-env-changed=PYO3_CROSS_PYTHON_VERSION
cargo:rerun-if-env-changed=PYO3_CROSS_PYTHON_IMPLEMENTATION
cargo:rerun-if-env-changed=PYO3_PRINT_CONFIG
cargo:PYO3_CONFIG=696d706c656d656e746174696f6e3d43507974686f6e0a76657273696f6e3d332e31330a7368617265643d747275650a616269333d66616c73650a6c69625f6e616d653d707974686f6e3331330a6c69625f6469723d433a5c507974686f6e3331335c6c6962730a65786563757461626c653d433a5c507974686f6e3331335c707974686f6e2e6578650a706f696e7465725f77696474683d36340a6275696c645f666c6167733d0a73757070726573735f6275696c645f7363726970745f6c696e6b5f6c696e65733d66616c73650a
cargo:rustc-link-lib=pythonXY:python313
cargo:rustc-link-search=native=C:\Python313\libs
cargo:rustc-cfg=Py_3_6
cargo:rustc-cfg=Py_3_7
cargo:rustc-cfg=Py_3_8
cargo:rustc-cfg=Py_3_9
cargo:rustc-cfg=Py_3_10
cargo:rustc-cfg=Py_3_11
cargo:rustc-cfg=Py_3_12
cargo:rustc-cfg=Py_3_13
cargo:rustc-cfg=invalid_from_utf8_lint
cargo:rustc-cfg=c_str_lit
cargo:rustc-cfg=diagnostic_namespace
