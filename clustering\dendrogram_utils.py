"""
Dendrogram Visualization Utilities for Dynamic FX Clustering Application

This module provides utilities for creating interactive dendrogram visualizations
from hierarchical clustering results, including linkage matrix processing and
Plotly dendrogram generation.
"""

import numpy as np
import plotly.graph_objects as go
import plotly.figure_factory as ff
from typing import List, Dict, Tuple, Optional, Any
import logging
from scipy.cluster.hierarchy import dendrogram as scipy_dendrogram
from scipy.cluster.hierarchy import linkage as scipy_linkage
import pandas as pd

# Configure logging
logger = logging.getLogger(__name__)


def create_interactive_dendrogram(
    correlation_matrix: np.ndarray,
    symbols: List[str],
    linkage_matrix: Optional[List[List[float]]] = None,
    cluster_assignments: Optional[List[int]] = None,
    title: str = "Currency Pair Clustering Dendrogram",
    height: int = 500,
    color_threshold: Optional[float] = None
) -> go.Figure:
    """
    Create an interactive dendrogram from correlation matrix and clustering results
    
    Args:
        correlation_matrix: Correlation matrix (n x n)
        symbols: List of currency pair symbols
        linkage_matrix: Optional linkage matrix from Rust clustering
        cluster_assignments: Optional cluster assignments
        title: Chart title
        height: Chart height in pixels
        color_threshold: Threshold for coloring clusters
        
    Returns:
        Plotly Figure object with interactive dendrogram
    """
    try:
        if correlation_matrix.size == 0 or len(symbols) == 0:
            return _create_empty_dendrogram(title, height, "No data available")
        
        # Convert correlation to distance matrix
        distance_matrix = correlation_to_distance_matrix(correlation_matrix)
        
        # Create linkage matrix if not provided
        if linkage_matrix is None:
            # Use scipy for linkage calculation as fallback
            condensed_distances = squareform(distance_matrix)
            linkage_matrix = scipy_linkage(condensed_distances, method='complete')
        else:
            # Convert Rust linkage matrix format to scipy format
            linkage_matrix = np.array(linkage_matrix)
        
        # Create dendrogram using Plotly
        fig = ff.create_dendrogram(
            distance_matrix,
            labels=symbols,
            linkagefun=lambda x: linkage_matrix,
            color_threshold=color_threshold,
            orientation='bottom'
        )
        
        # Enhance the dendrogram with interactivity
        fig = _enhance_dendrogram_interactivity(fig, symbols, cluster_assignments)
        
        # Update layout
        fig.update_layout(
            title=title,
            height=height,
            xaxis_title="Currency Pairs",
            yaxis_title="Distance",
            template='plotly_dark',
            showlegend=True,
            hovermode='closest',
            font=dict(size=10)
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating dendrogram: {str(e)}")
        return _create_empty_dendrogram(title, height, f"Error: {str(e)[:50]}")


def create_cluster_scatter_plot(
    symbols: List[str],
    cluster_assignments: List[int],
    correlation_matrix: Optional[np.ndarray] = None,
    title: str = "Currency Pair Clusters",
    height: int = 500
) -> go.Figure:
    """
    Create a scatter plot visualization of cluster assignments
    
    Args:
        symbols: List of currency pair symbols
        cluster_assignments: Cluster assignment for each symbol
        correlation_matrix: Optional correlation matrix for positioning
        title: Chart title
        height: Chart height in pixels
        
    Returns:
        Plotly Figure object with cluster scatter plot
    """
    try:
        if len(symbols) == 0 or len(cluster_assignments) == 0:
            return _create_empty_dendrogram(title, height, "No cluster data available")
        
        # Create cluster visualization
        fig = go.Figure()
        
        # Color palette for clusters
        colors = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ]
        
        # Group symbols by cluster
        clusters = {}
        for i, (symbol, cluster_id) in enumerate(zip(symbols, cluster_assignments)):
            if cluster_id not in clusters:
                clusters[cluster_id] = {'symbols': [], 'indices': []}
            clusters[cluster_id]['symbols'].append(symbol)
            clusters[cluster_id]['indices'].append(i)
        
        # Create scatter traces for each cluster
        for cluster_id, cluster_data in clusters.items():
            color = colors[cluster_id % len(colors)]
            
            # Position calculation (simple grid layout)
            n_symbols = len(cluster_data['symbols'])
            x_positions = cluster_data['indices']
            y_positions = [cluster_id] * n_symbols
            
            # Add scatter trace
            fig.add_trace(go.Scatter(
                x=x_positions,
                y=y_positions,
                mode='markers+text',
                text=cluster_data['symbols'],
                textposition='middle center',
                marker=dict(
                    size=25,
                    color=color,
                    line=dict(width=2, color='white'),
                    opacity=0.8
                ),
                name=f'Cluster {cluster_id}',
                hovertemplate='<b>%{text}</b><br>Cluster: %{y}<br>Index: %{x}<extra></extra>',
                textfont=dict(size=8, color='white')
            ))
        
        # Update layout
        fig.update_layout(
            title=title,
            height=height,
            xaxis_title="Symbol Index",
            yaxis_title="Cluster ID",
            template='plotly_dark',
            showlegend=True,
            hovermode='closest',
            xaxis=dict(showgrid=True, gridcolor='rgba(128,128,128,0.3)'),
            yaxis=dict(showgrid=True, gridcolor='rgba(128,128,128,0.3)')
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating cluster scatter plot: {str(e)}")
        return _create_empty_dendrogram(title, height, f"Error: {str(e)[:50]}")


def correlation_to_distance_matrix(correlation_matrix: np.ndarray) -> np.ndarray:
    """
    Convert correlation matrix to distance matrix using sqrt(2 * (1 - correlation))
    
    Args:
        correlation_matrix: Correlation matrix (n x n)
        
    Returns:
        Distance matrix (n x n)
    """
    # Clamp correlations to [-1, 1] to avoid numerical issues
    clamped_corr = np.clip(correlation_matrix, -1.0, 1.0)
    
    # Calculate distance: sqrt(2 * (1 - correlation))
    distance_matrix = np.sqrt(2.0 * (1.0 - clamped_corr))
    
    # Ensure diagonal is zero
    np.fill_diagonal(distance_matrix, 0.0)
    
    return distance_matrix


def squareform(distance_matrix: np.ndarray) -> np.ndarray:
    """
    Convert square distance matrix to condensed form for scipy
    
    Args:
        distance_matrix: Square distance matrix (n x n)
        
    Returns:
        Condensed distance array
    """
    n = distance_matrix.shape[0]
    condensed = []
    
    for i in range(n):
        for j in range(i + 1, n):
            condensed.append(distance_matrix[i, j])
    
    return np.array(condensed)


def _enhance_dendrogram_interactivity(
    fig: go.Figure,
    symbols: List[str],
    cluster_assignments: Optional[List[int]] = None
) -> go.Figure:
    """
    Enhance dendrogram with interactive features
    
    Args:
        fig: Base dendrogram figure
        symbols: Currency pair symbols
        cluster_assignments: Optional cluster assignments for coloring
        
    Returns:
        Enhanced figure with interactivity
    """
    try:
        # Add hover information
        for trace in fig.data:
            if hasattr(trace, 'text') and trace.text:
                # Enhance hover template
                trace.hovertemplate = '<b>%{text}</b><br>Distance: %{y:.3f}<extra></extra>'
        
        # Add cluster assignment information if available
        if cluster_assignments:
            # Create cluster color mapping
            unique_clusters = list(set(cluster_assignments))
            cluster_colors = {
                cluster: f'hsl({(cluster * 360 / len(unique_clusters)) % 360}, 70%, 50%)'
                for cluster in unique_clusters
            }
            
            # Update trace colors based on clusters
            for i, trace in enumerate(fig.data):
                if i < len(cluster_assignments):
                    cluster_id = cluster_assignments[i]
                    trace.marker.color = cluster_colors.get(cluster_id, 'gray')
        
        return fig
        
    except Exception as e:
        logger.warning(f"Could not enhance dendrogram interactivity: {str(e)}")
        return fig


def _create_empty_dendrogram(title: str, height: int, message: str) -> go.Figure:
    """
    Create an empty dendrogram figure with a message
    
    Args:
        title: Chart title
        height: Chart height
        message: Message to display
        
    Returns:
        Empty figure with message
    """
    fig = go.Figure()
    
    fig.add_annotation(
        text=message,
        xref="paper", yref="paper",
        x=0.5, y=0.5,
        xanchor='center', yanchor='middle',
        showarrow=False,
        font=dict(size=16, color="gray")
    )
    
    fig.update_layout(
        title=title,
        height=height,
        template='plotly_dark',
        showlegend=False,
        xaxis=dict(visible=False),
        yaxis=dict(visible=False)
    )
    
    return fig


def calculate_cluster_statistics(
    symbols: List[str],
    cluster_assignments: List[int],
    correlation_matrix: np.ndarray
) -> Dict[str, Any]:
    """
    Calculate statistics for each cluster
    
    Args:
        symbols: Currency pair symbols
        cluster_assignments: Cluster assignments
        correlation_matrix: Correlation matrix
        
    Returns:
        Dictionary with cluster statistics
    """
    try:
        stats = {}
        
        # Group by clusters
        clusters = {}
        for i, (symbol, cluster_id) in enumerate(zip(symbols, cluster_assignments)):
            if cluster_id not in clusters:
                clusters[cluster_id] = []
            clusters[cluster_id].append(i)
        
        # Calculate statistics for each cluster
        for cluster_id, indices in clusters.items():
            cluster_symbols = [symbols[i] for i in indices]
            
            # Calculate intra-cluster correlations
            if len(indices) > 1:
                cluster_corr_matrix = correlation_matrix[np.ix_(indices, indices)]
                # Get upper triangle (excluding diagonal)
                upper_triangle = np.triu(cluster_corr_matrix, k=1)
                intra_correlations = upper_triangle[upper_triangle != 0]
                
                avg_correlation = np.mean(intra_correlations)
                min_correlation = np.min(intra_correlations)
                max_correlation = np.max(intra_correlations)
            else:
                avg_correlation = min_correlation = max_correlation = 1.0
            
            stats[cluster_id] = {
                'size': len(indices),
                'symbols': cluster_symbols,
                'avg_correlation': avg_correlation,
                'min_correlation': min_correlation,
                'max_correlation': max_correlation
            }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error calculating cluster statistics: {str(e)}")
        return {}
