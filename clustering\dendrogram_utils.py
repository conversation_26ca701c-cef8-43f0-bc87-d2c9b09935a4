"""
Dendrogram Visualization Utilities for Dynamic FX Clustering Application

This module provides utilities for creating interactive dendrogram visualizations
from hierarchical clustering results, including linkage matrix processing and
Plotly dendrogram generation.
"""

import numpy as np
import plotly.graph_objects as go
import plotly.figure_factory as ff
from typing import List, Dict, Tuple, Optional, Any
import logging
from scipy.cluster.hierarchy import dendrogram as scipy_dendrogram
from scipy.cluster.hierarchy import linkage as scipy_linkage
from scipy.spatial.distance import squareform
import pandas as pd
import random

# Configure logging
logger = logging.getLogger(__name__)


def create_cluster_visualization(symbols: List[str], cluster_assignments: List[int]) -> go.Figure:
    """
    Create a proper cluster visualization that actually represents our cluster assignments.
    Instead of a misleading dendrogram, this creates a clear cluster layout.

    Args:
        symbols: List of currency pair symbols
        cluster_assignments: Cluster ID for each symbol

    Returns:
        Plotly Figure object showing clusters
    """
    if not symbols or not cluster_assignments or len(symbols) != len(cluster_assignments):
        return _create_empty_dendrogram("Cluster Visualization", 500, "No cluster data available")

    # Group symbols by cluster
    clusters = {}
    for symbol, cluster_id in zip(symbols, cluster_assignments):
        if cluster_id not in clusters:
            clusters[cluster_id] = []
        clusters[cluster_id].append(symbol)

    # Define cluster colors
    cluster_colors = [
        '#1f77b4',  # Blue
        '#ff7f0e',  # Orange
        '#2ca02c',  # Green
        '#d62728',  # Red
        '#9467bd',  # Purple
        '#8c564b',  # Brown
        '#e377c2',  # Pink
        '#7f7f7f',  # Gray
        '#bcbd22',  # Olive
        '#17becf'   # Cyan
    ]

    fig = go.Figure()

    # Calculate layout positions
    cluster_ids = sorted(clusters.keys())
    num_clusters = len(cluster_ids)

    # Create cluster layout - arrange clusters horizontally
    cluster_width = 1.0 / num_clusters
    y_positions = {}

    for i, cluster_id in enumerate(cluster_ids):
        cluster_symbols = clusters[cluster_id]
        cluster_size = len(cluster_symbols)

        # Calculate x position for this cluster
        cluster_center_x = (i + 0.5) * cluster_width

        # Calculate y positions for symbols in this cluster
        if cluster_size == 1:
            y_positions[cluster_symbols[0]] = 0.5
        else:
            for j, symbol in enumerate(cluster_symbols):
                y_positions[symbol] = 0.1 + (j / (cluster_size - 1)) * 0.8

        # Add cluster symbols as scatter points
        x_positions = [cluster_center_x] * cluster_size
        y_vals = [y_positions[symbol] for symbol in cluster_symbols]

        color = cluster_colors[cluster_id % len(cluster_colors)]

        # Add scatter trace for this cluster
        fig.add_trace(go.Scatter(
            x=x_positions,
            y=y_vals,
            mode='markers+text',
            text=cluster_symbols,
            textposition='middle right',
            textfont=dict(size=10, color='white'),
            marker=dict(
                size=15,
                color=color,
                line=dict(width=2, color='white')
            ),
            name=f'Cluster {cluster_id}',
            hovertemplate='<b>%{text}</b><br>Cluster: ' + str(cluster_id) + '<extra></extra>',
            customdata=[cluster_id] * cluster_size
        ))

        # Add cluster label
        fig.add_annotation(
            x=cluster_center_x,
            y=-0.1,
            text=f'<b>Cluster {cluster_id}</b><br>({cluster_size} pairs)',
            showarrow=False,
            font=dict(size=12, color=color),
            xanchor='center'
        )

    # Update layout
    fig.update_layout(
        title={
            'text': 'Currency Pair Clusters',
            'x': 0.5,
            'xanchor': 'center',
            'font': {'size': 16, 'color': 'white'}
        },
        xaxis=dict(
            showgrid=False,
            showticklabels=False,
            zeroline=False,
            range=[-0.05, 1.05]
        ),
        yaxis=dict(
            showgrid=False,
            showticklabels=False,
            zeroline=False,
            range=[-0.2, 1.0]
        ),
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="right",
            x=1,
            font=dict(color='white')
        ),
        plot_bgcolor='rgba(0,0,0,0)',
        paper_bgcolor='rgba(0,0,0,0)',
        font=dict(color='white'),
        height=500,
        margin=dict(l=50, r=50, t=80, b=80)
    )

    return fig


def create_interactive_dendrogram(
    correlation_matrix: np.ndarray,
    symbols: List[str],
    linkage_matrix: Optional[List[List[float]]] = None,
    cluster_assignments: Optional[List[int]] = None,
    title: str = "Currency Pair Clustering Dendrogram",
    height: int = 500,
    color_threshold: Optional[float] = None
) -> go.Figure:
    """
    Create a cluster visualization that actually represents our cluster assignments.

    Args:
        correlation_matrix: Correlation matrix (n x n) - not used, kept for compatibility
        symbols: List of currency pair symbols
        linkage_matrix: Optional linkage matrix from Rust clustering - not used
        cluster_assignments: Cluster assignments for each symbol
        title: Chart title
        height: Chart height in pixels
        color_threshold: Threshold for coloring clusters - not used

    Returns:
        Plotly Figure object with cluster visualization
    """
    try:
        if len(symbols) == 0:
            return _create_empty_dendrogram(title, height, "No data available")

        # Use our custom cluster visualization instead of misleading dendrogram
        if cluster_assignments and len(cluster_assignments) == len(symbols):
            logger.info(f"Creating cluster visualization for {len(symbols)} symbols with {len(set(cluster_assignments))} clusters")
            return create_cluster_visualization(symbols, cluster_assignments)
        else:
            logger.warning("No valid cluster assignments, creating fallback visualization")
            # Create a fallback where each symbol is its own cluster
            fallback_assignments = list(range(len(symbols)))
            return create_cluster_visualization(symbols, fallback_assignments)

    except Exception as e:
        logger.error(f"Error creating cluster visualization: {str(e)}")
        return _create_empty_dendrogram(title, height, f"Error: {str(e)[:50]}")


def create_cluster_scatter_plot(
    symbols: List[str],
    cluster_assignments: List[int],
    correlation_matrix: Optional[np.ndarray] = None,
    title: str = "Currency Pair Clusters",
    height: int = 500
) -> go.Figure:
    """
    Create a scatter plot visualization of cluster assignments
    
    Args:
        symbols: List of currency pair symbols
        cluster_assignments: Cluster assignment for each symbol
        correlation_matrix: Optional correlation matrix for positioning
        title: Chart title
        height: Chart height in pixels
        
    Returns:
        Plotly Figure object with cluster scatter plot
    """
    try:
        if len(symbols) == 0 or len(cluster_assignments) == 0:
            return _create_empty_dendrogram(title, height, "No cluster data available")
        
        # Create cluster visualization
        fig = go.Figure()
        
        # Color palette for clusters
        colors = [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ]
        
        # Group symbols by cluster
        clusters = {}
        for i, (symbol, cluster_id) in enumerate(zip(symbols, cluster_assignments)):
            if cluster_id not in clusters:
                clusters[cluster_id] = {'symbols': [], 'indices': []}
            clusters[cluster_id]['symbols'].append(symbol)
            clusters[cluster_id]['indices'].append(i)
        
        # Create scatter traces for each cluster
        for cluster_id, cluster_data in clusters.items():
            color = colors[cluster_id % len(colors)]
            
            # Position calculation (simple grid layout)
            n_symbols = len(cluster_data['symbols'])
            x_positions = cluster_data['indices']
            y_positions = [cluster_id] * n_symbols
            
            # Add scatter trace
            fig.add_trace(go.Scatter(
                x=x_positions,
                y=y_positions,
                mode='markers+text',
                text=cluster_data['symbols'],
                textposition='middle center',
                marker=dict(
                    size=25,
                    color=color,
                    line=dict(width=2, color='white'),
                    opacity=0.8
                ),
                name=f'Cluster {cluster_id}',
                hovertemplate='<b>%{text}</b><br>Cluster: %{y}<br>Index: %{x}<extra></extra>',
                textfont=dict(size=8, color='white')
            ))
        
        # Update layout
        fig.update_layout(
            title=title,
            height=height,
            xaxis_title="Symbol Index",
            yaxis_title="Cluster ID",
            template='plotly_dark',
            showlegend=True,
            hovermode='closest',
            xaxis=dict(showgrid=True, gridcolor='rgba(128,128,128,0.3)'),
            yaxis=dict(showgrid=True, gridcolor='rgba(128,128,128,0.3)')
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"Error creating cluster scatter plot: {str(e)}")
        return _create_empty_dendrogram(title, height, f"Error: {str(e)[:50]}")


def correlation_to_distance_matrix(correlation_matrix: np.ndarray) -> np.ndarray:
    """
    Convert correlation matrix to distance matrix using sqrt(2 * (1 - correlation))
    
    Args:
        correlation_matrix: Correlation matrix (n x n)
        
    Returns:
        Distance matrix (n x n)
    """
    # Clamp correlations to [-1, 1] to avoid numerical issues
    clamped_corr = np.clip(correlation_matrix, -1.0, 1.0)
    
    # Calculate distance: sqrt(2 * (1 - correlation))
    distance_matrix = np.sqrt(2.0 * (1.0 - clamped_corr))
    
    # Ensure diagonal is zero
    np.fill_diagonal(distance_matrix, 0.0)
    
    return distance_matrix


def squareform(distance_matrix: np.ndarray) -> np.ndarray:
    """
    Convert square distance matrix to condensed form for scipy
    
    Args:
        distance_matrix: Square distance matrix (n x n)
        
    Returns:
        Condensed distance array
    """
    n = distance_matrix.shape[0]
    condensed = []
    
    for i in range(n):
        for j in range(i + 1, n):
            condensed.append(distance_matrix[i, j])
    
    return np.array(condensed)


def _enhance_dendrogram_interactivity(
    fig: go.Figure,
    symbols: List[str],
    cluster_assignments: Optional[List[int]] = None
) -> go.Figure:
    """
    Enhance dendrogram with interactive features including clickable nodes

    Args:
        fig: Base dendrogram figure
        symbols: Currency pair symbols
        cluster_assignments: Optional cluster assignments for coloring

    Returns:
        Enhanced figure with interactivity
    """
    try:
        logger.info(f"Enhancing dendrogram interactivity for {len(symbols)} symbols")

        # Make all existing traces clickable
        for i, trace in enumerate(fig.data):
            logger.debug(f"Processing trace {i}: type={type(trace).__name__}")

            if hasattr(trace, 'hovertemplate'):
                # Enhance hover template for dendrogram nodes
                trace.hovertemplate = 'Distance: %{y:.3f}<br>Click to select cluster<extra></extra>'
                logger.debug(f"Enhanced hover for trace {i}")

            # Ensure traces are clickable by adding markers
            if hasattr(trace, 'mode') and hasattr(trace, 'x') and hasattr(trace, 'y'):
                if 'markers' not in str(trace.mode):
                    trace.mode = trace.mode + '+markers' if trace.mode else 'markers'

                # Add visible markers for dendrogram nodes
                if hasattr(trace, 'marker'):
                    trace.marker.size = 6
                    trace.marker.color = 'rgba(255, 255, 255, 0.8)'
                    trace.marker.line = dict(width=1, color='black')

                logger.debug(f"Made trace {i} clickable with markers")

        # Add symbol labels as clickable scatter points at the bottom
        if symbols:
            # Create cluster color mapping if available
            cluster_colors = {}
            if cluster_assignments:
                unique_clusters = list(set(cluster_assignments))
                cluster_colors = {
                    cluster: f'hsl({(cluster * 360 / len(unique_clusters)) % 360}, 70%, 50%)'
                    for cluster in unique_clusters
                }

            # Find the x positions of the symbols (should be at the bottom of dendrogram)
            x_positions = list(range(len(symbols)))
            y_position = 0  # Bottom of the dendrogram

            # Determine colors for symbols based on cluster assignments
            symbol_colors = []
            if cluster_assignments and len(cluster_assignments) == len(symbols):
                symbol_colors = [cluster_colors.get(cluster_assignments[i], 'gray') for i in range(len(symbols))]
            else:
                symbol_colors = ['lightblue'] * len(symbols)

            # Add clickable scatter trace for symbols
            symbol_trace = go.Scatter(
                x=x_positions,
                y=[y_position] * len(symbols),
                mode='markers+text',
                text=symbols,
                textposition='bottom center',
                marker=dict(
                    size=10,
                    color=symbol_colors,
                    opacity=0.8,
                    line=dict(width=1, color='black')
                ),
                hovertemplate='<b>%{text}</b><br>Click to select cluster<extra></extra>',
                name='Currency Pairs',
                showlegend=False
            )

            fig.add_trace(symbol_trace)
            logger.info(f"Added clickable symbol trace with {len(symbols)} points")
            logger.info(f"Applied cluster colors to {len(symbol_colors)} symbols")

        return fig

    except Exception as e:
        logger.warning(f"Could not enhance dendrogram interactivity: {str(e)}")
        return fig


def _create_empty_dendrogram(title: str, height: int, message: str) -> go.Figure:
    """
    Create an empty dendrogram figure with a message
    
    Args:
        title: Chart title
        height: Chart height
        message: Message to display
        
    Returns:
        Empty figure with message
    """
    fig = go.Figure()
    
    fig.add_annotation(
        text=message,
        xref="paper", yref="paper",
        x=0.5, y=0.5,
        xanchor='center', yanchor='middle',
        showarrow=False,
        font=dict(size=16, color="gray")
    )
    
    fig.update_layout(
        title=title,
        height=height,
        template='plotly_dark',
        showlegend=False,
        xaxis=dict(visible=False),
        yaxis=dict(visible=False)
    )
    
    return fig


def calculate_cluster_statistics(
    symbols: List[str],
    cluster_assignments: List[int],
    correlation_matrix: np.ndarray
) -> Dict[str, Any]:
    """
    Calculate statistics for each cluster
    
    Args:
        symbols: Currency pair symbols
        cluster_assignments: Cluster assignments
        correlation_matrix: Correlation matrix
        
    Returns:
        Dictionary with cluster statistics
    """
    try:
        stats = {}
        
        # Group by clusters
        clusters = {}
        for i, (symbol, cluster_id) in enumerate(zip(symbols, cluster_assignments)):
            if cluster_id not in clusters:
                clusters[cluster_id] = []
            clusters[cluster_id].append(i)
        
        # Calculate statistics for each cluster
        for cluster_id, indices in clusters.items():
            cluster_symbols = [symbols[i] for i in indices]
            
            # Calculate intra-cluster correlations
            if len(indices) > 1:
                cluster_corr_matrix = correlation_matrix[np.ix_(indices, indices)]
                # Get upper triangle (excluding diagonal)
                upper_triangle = np.triu(cluster_corr_matrix, k=1)
                intra_correlations = upper_triangle[upper_triangle != 0]
                
                avg_correlation = np.mean(intra_correlations)
                min_correlation = np.min(intra_correlations)
                max_correlation = np.max(intra_correlations)
            else:
                avg_correlation = min_correlation = max_correlation = 1.0
            
            stats[cluster_id] = {
                'size': len(indices),
                'symbols': cluster_symbols,
                'avg_correlation': avg_correlation,
                'min_correlation': min_correlation,
                'max_correlation': max_correlation
            }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error calculating cluster statistics: {str(e)}")
        return {}
