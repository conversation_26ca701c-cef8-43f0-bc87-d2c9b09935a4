{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"experimental-async\", \"gil-refs\", \"multiple-pymethods\"]", "target": 13917622123232857288, "profile": 7218910627595408246, "path": 15062646162288574738, "deps": [[3060637413840920116, "proc_macro2", false, 9742792036988073286], [3771343242488894766, "pyo3_macros_backend", false, 8740659399970677562], [4974441333307933176, "syn", false, 7602834080497375398], [17990358020177143287, "quote", false, 675908390363228251]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\pyo3-macros-19d20ab6b57dabe4\\dep-lib-pyo3_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}