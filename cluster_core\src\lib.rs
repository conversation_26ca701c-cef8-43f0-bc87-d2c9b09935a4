use pyo3::prelude::*;
use ndarray::{Array2, Axis};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Raw FX price data container for M1 timeframe
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FxPriceData {
    #[pyo3(get, set)]
    pub symbol: String,
    #[pyo3(get, set)]
    pub timestamp: i64,
    #[pyo3(get, set)]
    pub open: f64,
    #[pyo3(get, set)]
    pub high: f64,
    #[pyo3(get, set)]
    pub low: f64,
    #[pyo3(get, set)]
    pub close: f64,
    #[pyo3(get, set)]
    pub volume: f64,
}

#[pymethods]
impl FxPriceData {
    #[new]
    pub fn new(symbol: String, timestamp: i64, open: f64, high: f64, low: f64, close: f64, volume: f64) -> Self {
        Self {
            symbol,
            timestamp,
            open,
            high,
            low,
            close,
            volume,
        }
    }
}

/// Computed log returns matrix for correlation analysis
#[pyclass]
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LogReturns {
    #[pyo3(get, set)]
    pub symbols: Vec<String>,
    #[pyo3(get, set)]
    pub timestamps: Vec<i64>,
    pub returns: Array2<f64>,
}

#[pymethods]
impl LogReturns {
    #[new]
    pub fn new(symbols: Vec<String>, timestamps: Vec<i64>, returns_data: Vec<Vec<f64>>) -> PyResult<Self> {
        let rows = returns_data.len();
        let cols = if rows > 0 { returns_data[0].len() } else { 0 };

        let flat_data: Vec<f64> = returns_data.into_iter().flatten().collect();
        let returns = Array2::from_shape_vec((rows, cols), flat_data)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid array shape: {}", e)))?;

        Ok(Self {
            symbols,
            timestamps,
            returns,
        })
    }

    #[getter]
    pub fn get_returns(&self) -> Vec<Vec<f64>> {
        self.returns.outer_iter()
            .map(|row| row.to_vec())
            .collect()
    }
}

/// Correlation matrix with metadata for clustering
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorrelationMatrix {
    #[pyo3(get, set)]
    pub symbols: Vec<String>,
    #[pyo3(get, set)]
    pub timestamp: i64,
    pub matrix: Array2<f64>,
    #[pyo3(get, set)]
    pub window_size: usize,
}

#[pymethods]
impl CorrelationMatrix {
    #[new]
    pub fn new(symbols: Vec<String>, timestamp: i64, matrix_data: Vec<Vec<f64>>, window_size: usize) -> PyResult<Self> {
        let size = matrix_data.len();
        let flat_data: Vec<f64> = matrix_data.into_iter().flatten().collect();
        let matrix = Array2::from_shape_vec((size, size), flat_data)
            .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid matrix shape: {}", e)))?;

        Ok(Self {
            symbols,
            timestamp,
            matrix,
            window_size,
        })
    }

    #[getter]
    pub fn get_matrix(&self) -> Vec<Vec<f64>> {
        self.matrix.outer_iter()
            .map(|row| row.to_vec())
            .collect()
    }
}

/// Clustering result with assignments and linkage data
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ClusteringResult {
    #[pyo3(get, set)]
    pub symbols: Vec<String>,
    #[pyo3(get, set)]
    pub cluster_assignments: Vec<i32>,
    #[pyo3(get, set)]
    pub linkage_matrix: Vec<Vec<f64>>,
    #[pyo3(get, set)]
    pub distance_threshold: f64,
    #[pyo3(get, set)]
    pub timestamp: i64,
    #[pyo3(get, set)]
    pub num_clusters: usize,
}

#[pymethods]
impl ClusteringResult {
    #[new]
    pub fn new(
        symbols: Vec<String>,
        cluster_assignments: Vec<i32>,
        linkage_matrix: Vec<Vec<f64>>,
        distance_threshold: f64,
        timestamp: i64,
        num_clusters: usize,
    ) -> Self {
        Self {
            symbols,
            cluster_assignments,
            linkage_matrix,
            distance_threshold,
            timestamp,
            num_clusters,
        }
    }

    pub fn get_clusters(&self) -> HashMap<i32, Vec<String>> {
        let mut clusters = HashMap::new();
        for (i, &cluster_id) in self.cluster_assignments.iter().enumerate() {
            clusters.entry(cluster_id)
                .or_insert_with(Vec::new)
                .push(self.symbols[i].clone());
        }
        clusters
    }
}

/// Daily volatility profile for regime clustering
#[pyclass]
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VolatilityProfile {
    #[pyo3(get, set)]
    pub symbol: String,
    #[pyo3(get, set)]
    pub date: String,
    #[pyo3(get, set)]
    pub hourly_volatility: Vec<f64>,  // 24 hourly volatility values
    #[pyo3(get, set)]
    pub daily_volatility: f64,
    #[pyo3(get, set)]
    pub regime_id: Option<i32>,
}

#[pymethods]
impl VolatilityProfile {
    #[new]
    #[pyo3(signature = (symbol, date, hourly_volatility, daily_volatility, regime_id=None))]
    pub fn new(
        symbol: String,
        date: String,
        hourly_volatility: Vec<f64>,
        daily_volatility: f64,
        regime_id: Option<i32>,
    ) -> Self {
        Self {
            symbol,
            date,
            hourly_volatility,
            daily_volatility,
            regime_id,
        }
    }
}

/// Calculate log returns from price data
#[pyfunction]
pub fn calculate_log_returns(price_data: Vec<Vec<f64>>) -> PyResult<Vec<Vec<f64>>> {
    if price_data.is_empty() || price_data[0].is_empty() {
        return Ok(Vec::new());
    }

    let rows = price_data.len();
    let cols = price_data[0].len();

    // Convert to ndarray for efficient computation
    let flat_data: Vec<f64> = price_data.into_iter().flatten().collect();
    let prices = Array2::from_shape_vec((rows, cols), flat_data)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid price data shape: {}", e)))?;

    if rows < 2 {
        return Ok(Vec::new());
    }

    // Calculate log returns: ln(P_t / P_{t-1})
    let mut log_returns = Array2::<f64>::zeros((rows - 1, cols));

    for i in 1..rows {
        for j in 0..cols {
            let current_price = prices[[i, j]];
            let previous_price = prices[[i - 1, j]];

            if previous_price > 0.0 && current_price > 0.0 {
                log_returns[[i - 1, j]] = (current_price / previous_price).ln();
            } else {
                log_returns[[i - 1, j]] = 0.0; // Handle missing/invalid data
            }
        }
    }

    // Convert back to Vec<Vec<f64>>
    Ok(log_returns.outer_iter()
        .map(|row| row.to_vec())
        .collect())
}

/// Compute correlation matrix from log returns
#[pyfunction]
pub fn compute_correlation_matrix(log_returns: Vec<Vec<f64>>) -> PyResult<Vec<Vec<f64>>> {
    if log_returns.is_empty() || log_returns[0].is_empty() {
        return Ok(Vec::new());
    }

    let rows = log_returns.len();
    let cols = log_returns[0].len();

    // Convert to ndarray
    let flat_data: Vec<f64> = log_returns.into_iter().flatten().collect();
    let returns = Array2::from_shape_vec((rows, cols), flat_data)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid returns data shape: {}", e)))?;

    // Calculate means for each column (currency pair)
    let means = returns.mean_axis(Axis(0)).unwrap();

    // Center the data
    let centered = &returns - &means;

    // Calculate correlation matrix
    let mut correlation_matrix = Array2::<f64>::zeros((cols, cols));

    for i in 0..cols {
        for j in 0..cols {
            if i == j {
                correlation_matrix[[i, j]] = 1.0;
            } else {
                let col_i = centered.column(i);
                let col_j = centered.column(j);

                // Calculate Pearson correlation coefficient
                let numerator: f64 = col_i.iter().zip(col_j.iter()).map(|(a, b)| a * b).sum();
                let sum_sq_i: f64 = col_i.iter().map(|x| x * x).sum();
                let sum_sq_j: f64 = col_j.iter().map(|x| x * x).sum();

                let denominator = (sum_sq_i * sum_sq_j).sqrt();

                if denominator > 1e-10 {
                    correlation_matrix[[i, j]] = numerator / denominator;
                } else {
                    correlation_matrix[[i, j]] = 0.0;
                }
            }
        }
    }

    // Convert to Vec<Vec<f64>>
    Ok(correlation_matrix.outer_iter()
        .map(|row| row.to_vec())
        .collect())
}

/// Convert correlation matrix to distance matrix for clustering
#[pyfunction]
pub fn correlation_to_distance_matrix(correlation_matrix: Vec<Vec<f64>>) -> PyResult<Vec<Vec<f64>>> {
    if correlation_matrix.is_empty() {
        return Ok(Vec::new());
    }

    let size = correlation_matrix.len();
    let flat_data: Vec<f64> = correlation_matrix.into_iter().flatten().collect();
    let corr_matrix = Array2::from_shape_vec((size, size), flat_data)
        .map_err(|e| PyErr::new::<pyo3::exceptions::PyValueError, _>(format!("Invalid correlation matrix shape: {}", e)))?;

    // Calculate distance matrix using: distance = sqrt(2 * (1 - correlation))
    let distance_matrix = corr_matrix.mapv(|corr| {
        let clamped_corr = corr.max(-1.0).min(1.0); // Clamp to [-1, 1]
        (2.0 * (1.0 - clamped_corr)).sqrt()
    });

    // Convert to Vec<Vec<f64>>
    Ok(distance_matrix.outer_iter()
        .map(|row| row.to_vec())
        .collect())
}

/// Calculate rolling correlation matrix with specified window size
#[pyfunction]
pub fn rolling_correlation_matrix(
    price_data: Vec<Vec<f64>>,
    window_size: usize
) -> PyResult<Vec<Vec<Vec<f64>>>> {
    if price_data.len() < window_size + 1 {
        return Ok(Vec::new());
    }

    let mut rolling_correlations = Vec::new();

    // Calculate rolling windows
    for start_idx in 0..=(price_data.len() - window_size - 1) {
        let end_idx = start_idx + window_size + 1;
        let window_data = price_data[start_idx..end_idx].to_vec();

        // Calculate log returns for this window
        let log_returns = calculate_log_returns(window_data)?;

        // Calculate correlation matrix for this window
        if !log_returns.is_empty() {
            let correlation = compute_correlation_matrix(log_returns)?;
            rolling_correlations.push(correlation);
        }
    }

    Ok(rolling_correlations)
}

/// Perform hierarchical clustering using distance matrix
#[pyfunction]
pub fn perform_hierarchical_clustering(
    distance_matrix: Vec<Vec<f64>>,
    linkage_method: String,
) -> PyResult<(Vec<Vec<f64>>, Vec<i32>)> {
    if distance_matrix.is_empty() {
        return Ok((Vec::new(), Vec::new()));
    }

    let n = distance_matrix.len();
    if n < 2 {
        return Ok((Vec::new(), vec![0]));
    }

    // Convert to condensed distance matrix (upper triangle)
    let mut condensed_distances = Vec::new();
    for i in 0..n {
        for j in (i + 1)..n {
            condensed_distances.push(distance_matrix[i][j]);
        }
    }

    // Perform hierarchical clustering
    let linkage_matrix = match linkage_method.as_str() {
        "ward" => ward_linkage(&condensed_distances, n)?,
        "complete" => complete_linkage(&condensed_distances, n)?,
        "average" => average_linkage(&condensed_distances, n)?,
        _ => return Err(PyErr::new::<pyo3::exceptions::PyValueError, _>(
            format!("Unsupported linkage method: {}", linkage_method)
        )),
    };

    // Generate cluster assignments with fixed cluster count for forex analysis
    // Use 6 clusters for 28 currency pairs to create meaningful groupings
    let target_clusters = if n <= 10 { 3 } else if n <= 20 { 5 } else { 6 };
    let cluster_assignments = extract_cluster_assignments_simple(linkage_matrix.clone(), target_clusters, n);

    Ok((linkage_matrix, cluster_assignments))
}

/// Extract cluster assignments from linkage matrix with given threshold
#[pyfunction]
pub fn extract_cluster_assignments(
    linkage_matrix: Vec<Vec<f64>>,
    threshold: f64,
    n_samples: usize,
) -> Vec<i32> {
    if linkage_matrix.is_empty() || n_samples == 0 {
        return Vec::new();
    }

    // Initialize cluster assignments (each point starts in its own cluster)
    let mut cluster_assignments = (0..n_samples as i32).collect::<Vec<i32>>();
    let mut next_cluster_id = n_samples as i32;

    // Process linkage matrix to merge clusters below threshold
    for merge in linkage_matrix {
        if merge.len() < 3 {
            continue;
        }

        let cluster1 = merge[0] as usize;
        let cluster2 = merge[1] as usize;
        let distance = merge[2];

        if distance <= threshold {
            // Merge clusters
            let new_cluster_id = next_cluster_id;
            next_cluster_id += 1;

            // Update all points in cluster1 and cluster2
            for assignment in cluster_assignments.iter_mut() {
                if *assignment == cluster1 as i32 || *assignment == cluster2 as i32 {
                    *assignment = new_cluster_id;
                }
            }
        }
    }

    // Renumber clusters to be consecutive starting from 0
    let mut cluster_map = HashMap::new();
    let mut next_id = 0;

    for assignment in cluster_assignments.iter_mut() {
        if !cluster_map.contains_key(assignment) {
            cluster_map.insert(*assignment, next_id);
            next_id += 1;
        }
        *assignment = cluster_map[assignment];
    }

    cluster_assignments
}

/// Extract cluster assignments from linkage matrix with fixed cluster count
fn extract_cluster_assignments_fixed_count(
    linkage_matrix: Vec<Vec<f64>>,
    target_clusters: usize,
    n_samples: usize,
) -> Vec<i32> {
    if linkage_matrix.is_empty() || n_samples == 0 || target_clusters == 0 {
        return Vec::new();
    }

    // Initialize cluster assignments (each point starts in its own cluster)
    let mut cluster_assignments = (0..n_samples as i32).collect::<Vec<i32>>();
    let mut next_cluster_id = n_samples as i32;
    let mut current_cluster_count = n_samples;

    // Sort linkage matrix by distance (ascending) to merge closest clusters first
    let mut sorted_merges = linkage_matrix.clone();
    sorted_merges.sort_by(|a, b| a[2].partial_cmp(&b[2]).unwrap_or(std::cmp::Ordering::Equal));

    // Process linkage matrix to merge clusters until we reach target count
    for merge in sorted_merges {
        if current_cluster_count <= target_clusters {
            break;
        }

        if merge.len() < 3 {
            continue;
        }

        let cluster1 = merge[0] as usize;
        let cluster2 = merge[1] as usize;

        // Check if these clusters still exist (haven't been merged already)
        let cluster1_exists = cluster_assignments.iter().any(|&x| x == cluster1 as i32);
        let cluster2_exists = cluster_assignments.iter().any(|&x| x == cluster2 as i32);

        if cluster1_exists && cluster2_exists {
            // Merge clusters
            let new_cluster_id = next_cluster_id;
            next_cluster_id += 1;

            // Update all points in cluster1 and cluster2
            for assignment in cluster_assignments.iter_mut() {
                if *assignment == cluster1 as i32 || *assignment == cluster2 as i32 {
                    *assignment = new_cluster_id;
                }
            }

            current_cluster_count -= 1; // We merged two clusters into one
        }
    }

    // Renumber clusters to be consecutive starting from 0
    let mut cluster_map = HashMap::new();
    let mut next_id = 0;

    for assignment in cluster_assignments.iter_mut() {
        if !cluster_map.contains_key(assignment) {
            cluster_map.insert(*assignment, next_id);
            next_id += 1;
        }
        *assignment = cluster_map[assignment];
    }

    cluster_assignments
}

/// Correlation-based cluster assignment using K-means approach
fn extract_cluster_assignments_simple(
    linkage_matrix: Vec<Vec<f64>>,
    target_clusters: usize,
    n_samples: usize,
) -> Vec<i32> {
    if n_samples == 0 || target_clusters == 0 {
        return Vec::new();
    }

    // If we have fewer samples than target clusters, each sample gets its own cluster
    if n_samples <= target_clusters {
        return (0..n_samples as i32).collect();
    }

    // Use linkage matrix to determine cluster assignments
    // The linkage matrix contains merge information - we'll cut it to get target clusters
    let mut cluster_assignments = (0..n_samples as i32).collect::<Vec<i32>>();
    let mut next_cluster_id = n_samples as i32;
    let mut current_cluster_count = n_samples;

    // Sort merges by distance (ascending) to merge closest pairs first
    let mut sorted_merges = linkage_matrix.clone();
    sorted_merges.sort_by(|a, b| {
        if a.len() >= 3 && b.len() >= 3 {
            a[2].partial_cmp(&b[2]).unwrap_or(std::cmp::Ordering::Equal)
        } else {
            std::cmp::Ordering::Equal
        }
    });

    // Merge clusters until we reach target count
    for merge in sorted_merges {
        if current_cluster_count <= target_clusters {
            break;
        }

        if merge.len() < 3 {
            continue;
        }

        let cluster1_idx = merge[0] as usize;
        let cluster2_idx = merge[1] as usize;

        // Find current cluster IDs for these indices
        let cluster1_id = if cluster1_idx < cluster_assignments.len() {
            cluster_assignments[cluster1_idx]
        } else {
            continue;
        };

        let cluster2_id = if cluster2_idx < cluster_assignments.len() {
            cluster_assignments[cluster2_idx]
        } else {
            continue;
        };

        // Only merge if they're different clusters
        if cluster1_id != cluster2_id {
            let new_cluster_id = next_cluster_id;
            next_cluster_id += 1;

            // Update all points in both clusters
            for assignment in cluster_assignments.iter_mut() {
                if *assignment == cluster1_id || *assignment == cluster2_id {
                    *assignment = new_cluster_id;
                }
            }

            current_cluster_count -= 1;
        }
    }

    // Renumber clusters to be consecutive starting from 0
    let mut cluster_map = HashMap::new();
    let mut next_id = 0;

    for assignment in cluster_assignments.iter_mut() {
        if !cluster_map.contains_key(assignment) {
            cluster_map.insert(*assignment, next_id);
            next_id += 1;
        }
        *assignment = cluster_map[assignment];
    }

    cluster_assignments
}

/// Ward linkage clustering implementation
fn ward_linkage(distances: &[f64], n: usize) -> PyResult<Vec<Vec<f64>>> {
    // Simplified Ward linkage - for full implementation would need cluster sizes
    complete_linkage(distances, n) // Fallback to complete linkage for now
}

/// Complete linkage clustering implementation with proper scipy format
fn complete_linkage(distances: &[f64], n: usize) -> PyResult<Vec<Vec<f64>>> {
    let mut linkage_matrix = Vec::new();
    let distance_matrix = reconstruct_distance_matrix(distances, n);

    // Track cluster assignments for each merge step
    let mut cluster_map: Vec<usize> = (0..n).collect(); // Maps original indices to current cluster IDs
    let mut next_cluster_id = n; // Next available cluster ID
    let mut active_clusters: Vec<Vec<usize>> = (0..n).map(|i| vec![i]).collect();

    for step in 0..(n - 1) {
        // Find the pair of clusters with minimum maximum distance
        let mut min_distance = f64::INFINITY;
        let mut merge_i = 0;
        let mut merge_j = 1;

        for i in 0..active_clusters.len() {
            for j in (i + 1)..active_clusters.len() {
                let max_dist = calculate_complete_distance(&active_clusters[i], &active_clusters[j], &distance_matrix);
                if max_dist < min_distance {
                    min_distance = max_dist;
                    merge_i = i;
                    merge_j = j;
                }
            }
        }

        // Get cluster IDs for the merge (use the cluster ID that represents each cluster)
        let cluster_id_i = if active_clusters[merge_i].len() == 1 {
            active_clusters[merge_i][0] as f64  // Original point index
        } else {
            // For merged clusters, use the step number + n where this cluster was created
            (n + step - 1) as f64  // Previous merge created this cluster
        };

        let cluster_id_j = if active_clusters[merge_j].len() == 1 {
            active_clusters[merge_j][0] as f64  // Original point index
        } else {
            // For merged clusters, find when it was created
            (n + step - 1) as f64  // This is approximate - need better tracking
        };

        // Record the merge in scipy format: [cluster_i, cluster_j, distance, size]
        linkage_matrix.push(vec![
            cluster_id_i.min(cluster_id_j),  // Ensure smaller ID comes first
            cluster_id_i.max(cluster_id_j),  // Larger ID comes second
            min_distance,
            (active_clusters[merge_i].len() + active_clusters[merge_j].len()) as f64,
        ]);

        // Merge the clusters
        let mut new_cluster = active_clusters[merge_i].clone();
        new_cluster.extend(&active_clusters[merge_j]);

        // Remove old clusters (remove larger index first to avoid index shifting)
        if merge_i > merge_j {
            active_clusters.remove(merge_i);
            active_clusters.remove(merge_j);
        } else {
            active_clusters.remove(merge_j);
            active_clusters.remove(merge_i);
        }

        // Add the new merged cluster
        active_clusters.push(new_cluster);
        next_cluster_id += 1;
    }

    Ok(linkage_matrix)
}

/// Average linkage clustering implementation
fn average_linkage(distances: &[f64], n: usize) -> PyResult<Vec<Vec<f64>>> {
    let mut linkage_matrix = Vec::new();
    let mut clusters: Vec<Vec<usize>> = (0..n).map(|i| vec![i]).collect();
    let distance_matrix = reconstruct_distance_matrix(distances, n);

    while clusters.len() > 1 {
        // Find the pair of clusters with minimum average distance
        let mut min_distance = f64::INFINITY;
        let mut merge_i = 0;
        let mut merge_j = 1;

        for i in 0..clusters.len() {
            for j in (i + 1)..clusters.len() {
                let avg_dist = calculate_average_distance(&clusters[i], &clusters[j], &distance_matrix);
                if avg_dist < min_distance {
                    min_distance = avg_dist;
                    merge_i = i;
                    merge_j = j;
                }
            }
        }

        // Record the merge
        linkage_matrix.push(vec![
            clusters[merge_i][0] as f64,
            clusters[merge_j][0] as f64,
            min_distance,
            (clusters[merge_i].len() + clusters[merge_j].len()) as f64,
        ]);

        // Merge clusters
        let mut new_cluster = clusters[merge_i].clone();
        new_cluster.extend(&clusters[merge_j]);

        // Remove old clusters
        if merge_i > merge_j {
            clusters.remove(merge_i);
            clusters.remove(merge_j);
        } else {
            clusters.remove(merge_j);
            clusters.remove(merge_i);
        }

        clusters.push(new_cluster);
    }

    Ok(linkage_matrix)
}

/// Helper function to reconstruct full distance matrix from condensed form
fn reconstruct_distance_matrix(condensed: &[f64], n: usize) -> Vec<Vec<f64>> {
    let mut matrix = vec![vec![0.0; n]; n];
    let mut idx = 0;

    for i in 0..n {
        for j in (i + 1)..n {
            matrix[i][j] = condensed[idx];
            matrix[j][i] = condensed[idx];
            idx += 1;
        }
    }

    matrix
}

/// Calculate complete linkage distance between two clusters
fn calculate_complete_distance(
    cluster1: &[usize],
    cluster2: &[usize],
    distance_matrix: &[Vec<f64>],
) -> f64 {
    let mut max_distance = 0.0;

    for &i in cluster1 {
        for &j in cluster2 {
            let dist = distance_matrix[i][j];
            if dist > max_distance {
                max_distance = dist;
            }
        }
    }

    max_distance
}

/// Calculate average linkage distance between two clusters
fn calculate_average_distance(
    cluster1: &[usize],
    cluster2: &[usize],
    distance_matrix: &[Vec<f64>],
) -> f64 {
    let mut total_distance = 0.0;
    let mut count = 0;

    for &i in cluster1 {
        for &j in cluster2 {
            total_distance += distance_matrix[i][j];
            count += 1;
        }
    }

    if count > 0 {
        total_distance / count as f64
    } else {
        0.0
    }
}

/// Calculate default threshold for cluster extraction
fn calculate_default_threshold(linkage_matrix: &[Vec<f64>]) -> f64 {
    if linkage_matrix.is_empty() {
        return 0.0;
    }

    // Use 20% of the maximum distance as default threshold for much fewer, larger clusters
    // This creates meaningful multi-member clusters for forex analysis
    let max_distance = linkage_matrix
        .iter()
        .map(|merge| merge.get(2).unwrap_or(&0.0))
        .fold(0.0f64, |acc, &x| acc.max(x));

    max_distance * 0.2
}

/// Python module definition
#[pymodule]
fn cluster_core(m: &Bound<'_, PyModule>) -> PyResult<()> {
    // Add classes
    m.add_class::<FxPriceData>()?;
    m.add_class::<LogReturns>()?;
    m.add_class::<CorrelationMatrix>()?;
    m.add_class::<ClusteringResult>()?;
    m.add_class::<VolatilityProfile>()?;

    // Add mathematical functions
    m.add_function(wrap_pyfunction!(calculate_log_returns, m)?)?;
    m.add_function(wrap_pyfunction!(compute_correlation_matrix, m)?)?;
    m.add_function(wrap_pyfunction!(correlation_to_distance_matrix, m)?)?;
    m.add_function(wrap_pyfunction!(rolling_correlation_matrix, m)?)?;

    // Add clustering functions
    m.add_function(wrap_pyfunction!(perform_hierarchical_clustering, m)?)?;
    m.add_function(wrap_pyfunction!(extract_cluster_assignments, m)?)?;

    Ok(())
}
