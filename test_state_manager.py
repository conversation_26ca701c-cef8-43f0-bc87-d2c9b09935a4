"""
Test script for StateManager
Verifies state management, event detection, and persistence functionality
"""

import sys
import os
import logging
import numpy as np
from datetime import datetime, timedelta
import time

# Add clustering directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'clustering'))

from clustering.state_manager import StateManager, ClusteringState, ClusteringEvent
from clustering.data_manager import ClusteringDataManager
from config import CURRENCY_PAIRS, MARKET_TIMEZONE

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_mock_correlation_matrix(size: int, regime: str = "normal") -> np.ndarray:
    """Create mock correlation matrix for testing"""
    
    if regime == "normal":
        # Normal market conditions - moderate correlations
        base_corr = 0.3
        noise_level = 0.2
    elif regime == "crisis":
        # Crisis conditions - high correlations
        base_corr = 0.8
        noise_level = 0.1
    elif regime == "volatile":
        # Volatile conditions - mixed correlations
        base_corr = 0.1
        noise_level = 0.4
    else:
        base_corr = 0.5
        noise_level = 0.3
    
    # Create correlation matrix
    corr_matrix = np.full((size, size), base_corr)
    np.fill_diagonal(corr_matrix, 1.0)
    
    # Add noise
    noise = np.random.normal(0, noise_level, (size, size))
    noise = (noise + noise.T) / 2  # Make symmetric
    np.fill_diagonal(noise, 0)  # Keep diagonal as 1
    
    corr_matrix += noise
    
    # Ensure valid correlation matrix
    corr_matrix = np.clip(corr_matrix, -0.99, 0.99)
    np.fill_diagonal(corr_matrix, 1.0)
    
    return corr_matrix


def create_mock_cluster_assignments(size: int, regime: str = "normal") -> list:
    """Create mock cluster assignments for testing"""
    
    if regime == "normal":
        # 3-4 balanced clusters
        clusters = [0, 0, 1, 1, 2, 2, 3, 3] * (size // 8 + 1)
    elif regime == "crisis":
        # 1-2 large clusters (high correlation)
        clusters = [0, 0, 0, 0, 1, 1] * (size // 6 + 1)
    elif regime == "volatile":
        # Many small clusters
        clusters = list(range(size // 2)) * 2
    else:
        clusters = [0, 1, 2] * (size // 3 + 1)
    
    return clusters[:size]


def create_mock_volatility_profiles(symbols: list, regime: str = "normal") -> dict:
    """Create mock volatility profiles for testing"""
    
    if regime == "normal":
        base_vol = 0.01
        vol_range = 0.005
    elif regime == "crisis":
        base_vol = 0.03
        vol_range = 0.015
    elif regime == "volatile":
        base_vol = 0.02
        vol_range = 0.02
    else:
        base_vol = 0.015
        vol_range = 0.01
    
    return {
        symbol: base_vol + np.random.uniform(-vol_range, vol_range)
        for symbol in symbols
    }


def test_state_manager_basic():
    """Test basic StateManager functionality"""
    
    print("=" * 60)
    print("TESTING STATE MANAGER - BASIC FUNCTIONALITY")
    print("=" * 60)
    
    # Initialize StateManager
    print("\n1. Initializing StateManager...")
    state_manager = StateManager(
        max_history_size=100,
        event_threshold=0.7,
        persistence_dir="test_data/state"
    )
    print("✓ StateManager initialized")
    
    # Test symbols (subset for testing)
    test_symbols = CURRENCY_PAIRS[:8]
    print(f"\n2. Testing with {len(test_symbols)} symbols: {test_symbols}")
    
    # Create initial state
    print("\n3. Creating initial state...")
    corr_matrix = create_mock_correlation_matrix(len(test_symbols), "normal")
    cluster_assignments = create_mock_cluster_assignments(len(test_symbols), "normal")
    volatility_profiles = create_mock_volatility_profiles(test_symbols, "normal")
    
    event = state_manager.update_state(
        correlation_matrix=corr_matrix,
        cluster_assignments=cluster_assignments,
        symbols=test_symbols,
        volatility_profiles=volatility_profiles,
        data_quality_score=0.95
    )
    
    print(f"✓ Initial state created")
    print(f"  - Clusters: {len(set(cluster_assignments))}")
    print(f"  - Event detected: {event is not None}")
    
    # Get current state
    current_state = state_manager.get_current_state()
    if current_state:
        print(f"  - Regime stability: {current_state.regime_stability:.3f}")
        print(f"  - Data quality: {current_state.data_quality_score:.3f}")
    
    return state_manager, test_symbols


def test_event_detection(state_manager, test_symbols):
    """Test event detection functionality"""
    
    print("\n" + "=" * 60)
    print("TESTING EVENT DETECTION")
    print("=" * 60)
    
    print("\n1. Creating regime change scenario...")
    
    # Simulate regime changes
    regimes = ["normal", "crisis", "volatile", "normal"]
    events_detected = []
    
    for i, regime in enumerate(regimes):
        print(f"\n2.{i+1} Simulating '{regime}' regime...")
        
        corr_matrix = create_mock_correlation_matrix(len(test_symbols), regime)
        cluster_assignments = create_mock_cluster_assignments(len(test_symbols), regime)
        volatility_profiles = create_mock_volatility_profiles(test_symbols, regime)
        
        event = state_manager.update_state(
            correlation_matrix=corr_matrix,
            cluster_assignments=cluster_assignments,
            symbols=test_symbols,
            volatility_profiles=volatility_profiles,
            data_quality_score=0.90 - i * 0.05  # Gradually decreasing quality
        )
        
        if event:
            events_detected.append(event)
            print(f"  ✓ Event detected: {event.event_type}")
            print(f"    - ARI: {event.rand_index:.3f}")
            print(f"    - Significance: {event.significance_score:.3f}")
            print(f"    - Affected pairs: {len(event.affected_pairs)}")
            print(f"    - Description: {event.description}")
        else:
            print(f"  - No event detected")
        
        # Small delay to simulate real-time updates
        time.sleep(0.1)
    
    print(f"\n3. Event detection summary:")
    print(f"  - Total events detected: {len(events_detected)}")
    
    # Get recent events
    recent_events = state_manager.get_recent_events(hours_back=1)
    print(f"  - Recent events (1h): {len(recent_events)}")
    
    return events_detected


def test_performance_metrics(state_manager):
    """Test performance metrics functionality"""
    
    print("\n" + "=" * 60)
    print("TESTING PERFORMANCE METRICS")
    print("=" * 60)
    
    print("\n1. Getting performance metrics...")
    metrics = state_manager.get_performance_metrics()
    
    print(f"✓ Performance metrics retrieved:")
    print(f"  - Total updates: {metrics.total_updates}")
    print(f"  - Events detected: {metrics.events_detected}")
    print(f"  - Average processing time: {metrics.average_processing_time:.4f}s")
    print(f"  - Data quality average: {metrics.data_quality_average:.3f}")
    print(f"  - Uptime: {metrics.uptime_hours:.2f} hours")
    print(f"  - Last update: {metrics.last_update}")
    
    # Test stability trend
    print(f"\n2. Getting stability trend...")
    stability_trend = state_manager.get_cluster_stability_trend(hours_back=1)
    print(f"✓ Stability trend data points: {len(stability_trend)}")
    
    if stability_trend:
        latest_stability = stability_trend[-1][1]
        print(f"  - Latest stability: {latest_stability:.3f}")
    
    # Test state history
    print(f"\n3. Getting state history...")
    history = state_manager.get_state_history(limit=10)
    print(f"✓ Historical states retrieved: {len(history)}")
    
    return metrics


def test_persistence(state_manager):
    """Test state persistence functionality"""
    
    print("\n" + "=" * 60)
    print("TESTING STATE PERSISTENCE")
    print("=" * 60)
    
    print("\n1. Exporting state summary...")
    summary = state_manager.export_state_summary()
    
    print(f"✓ State summary exported:")
    print(f"  - System status: {summary['system_status']}")
    print(f"  - Total states recorded: {summary['total_states_recorded']}")
    print(f"  - Recent events count: {summary['recent_events_count']}")
    
    if summary['current_state']:
        current = summary['current_state']
        print(f"  - Current clusters: {current['cluster_count']}")
        print(f"  - Current stability: {current['regime_stability']:.3f}")
    
    # Test persistence by creating new StateManager
    print(f"\n2. Testing persistence recovery...")
    new_state_manager = StateManager(
        persistence_dir="test_data/state"
    )
    
    recovered_state = new_state_manager.get_current_state()
    if recovered_state:
        print(f"✓ State recovered successfully")
        print(f"  - Recovered timestamp: {recovered_state.timestamp}")
        print(f"  - Recovered clusters: {recovered_state.cluster_count}")
    else:
        print(f"- No state to recover (expected for first run)")
    
    return summary


def test_integration_with_data_manager():
    """Test integration with real data manager"""
    
    print("\n" + "=" * 60)
    print("TESTING INTEGRATION WITH DATA MANAGER")
    print("=" * 60)
    
    print("\n1. Initializing data manager...")
    data_manager = ClusteringDataManager()
    
    if not data_manager.connect():
        print("✗ Could not connect to MT5, skipping integration test")
        return
    
    print("✓ Connected to MT5")
    
    print("\n2. Fetching real market data...")
    test_pairs = CURRENCY_PAIRS[:5]  # Use subset for testing
    
    try:
        data = data_manager.fetch_clustering_data(
            pairs=test_pairs,
            hours_back=2,
            use_weekend_fallback=True
        )
        
        if data:
            print(f"✓ Fetched data for {len(data)} pairs")
            
            # Prepare data for clustering
            price_matrix = data_manager.prepare_data_for_rust(data)
            
            if price_matrix and len(price_matrix) > 10:
                print(f"✓ Prepared {len(price_matrix)} x {len(price_matrix[0])} price matrix")
                
                # Create simple correlation matrix from price data
                price_array = np.array(price_matrix)
                corr_matrix = np.corrcoef(price_array.T)
                
                # Simple clustering (for demonstration)
                cluster_assignments = [i % 3 for i in range(len(test_pairs))]
                
                # Mock volatility profiles
                volatility_profiles = {
                    pair: np.std(price_array[:, i]) if i < price_array.shape[1] else 0.01
                    for i, pair in enumerate(test_pairs)
                }
                
                # Test with StateManager
                state_manager = StateManager()
                event = state_manager.update_state(
                    correlation_matrix=corr_matrix,
                    cluster_assignments=cluster_assignments,
                    symbols=test_pairs,
                    volatility_profiles=volatility_profiles,
                    data_quality_score=0.95
                )
                
                print(f"✓ State updated with real data")
                print(f"  - Event detected: {event is not None}")
                
                current_state = state_manager.get_current_state()
                if current_state:
                    print(f"  - Clusters: {current_state.cluster_count}")
                    print(f"  - Stability: {current_state.regime_stability:.3f}")
            
        else:
            print("✗ No data fetched")
            
    except Exception as e:
        print(f"✗ Integration test error: {str(e)}")
        logger.error(f"Integration test error: {str(e)}", exc_info=True)
    
    finally:
        data_manager.disconnect()
        print("✓ Disconnected from MT5")


if __name__ == "__main__":
    print("Starting StateManager tests...")
    
    try:
        # Test basic functionality
        state_manager, test_symbols = test_state_manager_basic()
        
        # Test event detection
        events = test_event_detection(state_manager, test_symbols)
        
        # Test performance metrics
        metrics = test_performance_metrics(state_manager)
        
        # Test persistence
        summary = test_persistence(state_manager)
        
        # Test integration with data manager
        test_integration_with_data_manager()
        
        print("\n" + "=" * 60)
        print("ALL STATE MANAGER TESTS COMPLETED")
        print("=" * 60)
        print(f"🎉 StateManager is working correctly!")
        
    except Exception as e:
        print(f"\n💥 Test execution failed: {str(e)}")
        logger.error(f"Test execution error: {str(e)}", exc_info=True)
    
    print("\nStateManager test script finished.")
