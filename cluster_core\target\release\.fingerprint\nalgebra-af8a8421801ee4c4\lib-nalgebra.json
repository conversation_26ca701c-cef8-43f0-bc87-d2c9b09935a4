{"rustc": 1842507548689473721, "features": "[\"default\", \"macros\", \"matrixmultiply\", \"nalgebra-macros\", \"std\"]", "declared_features": "[\"alga\", \"alloc\", \"arbitrary\", \"bytemuck\", \"compare\", \"convert-bytemuck\", \"convert-glam014\", \"convert-glam015\", \"convert-glam016\", \"convert-glam017\", \"convert-glam018\", \"convert-glam019\", \"convert-glam020\", \"convert-glam021\", \"convert-glam022\", \"convert-glam023\", \"convert-glam024\", \"convert-glam025\", \"convert-glam027\", \"convert-mint\", \"cuda\", \"cust_core\", \"debug\", \"default\", \"glam014\", \"glam015\", \"glam016\", \"glam017\", \"glam018\", \"glam019\", \"glam020\", \"glam021\", \"glam022\", \"glam023\", \"glam024\", \"glam025\", \"glam027\", \"io\", \"libm\", \"libm-force\", \"macros\", \"matrixcompare-core\", \"matrixmultiply\", \"mint\", \"nalgebra-macros\", \"pest\", \"pest_derive\", \"proptest\", \"proptest-support\", \"quickcheck\", \"rand\", \"rand-no-std\", \"rand-package\", \"rand_distr\", \"rayon\", \"rkyv\", \"rkyv-safe-deser\", \"rkyv-serialize\", \"rkyv-serialize-no-std\", \"serde\", \"serde-serialize\", \"serde-serialize-no-std\", \"slow-tests\", \"sparse\", \"std\"]", "target": 572955357253318494, "profile": 2040997289075261528, "path": 11049639739129029512, "deps": [[2819946551904607991, "num_rational", false, 473070452859025341], [4462856585586636430, "simba", false, 3845615531765647860], [5157631553186200874, "num_traits", false, 8484145598454306592], [11394677342629719743, "nalgebra_macros", false, 9617465355284114483], [12319020793864570031, "num_complex", false, 9921354129542358373], [15677050387741058262, "approx", false, 17117016912115511553], [15826188163127377936, "matrixmultiply", false, 16907958943124577366], [17001665395952474378, "typenum", false, 17626787267160507072]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\nalgebra-af8a8421801ee4c4\\dep-lib-nalgebra", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}